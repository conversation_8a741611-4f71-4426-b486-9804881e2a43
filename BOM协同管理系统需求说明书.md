# BOM协同管理系统需求说明书

## 文档信息
- **项目名称**: BOM协同管理系统
- **文档版本**: V1.0
- **编写日期**: 2025-08-08
- **目标用户**: 小微制造企业

---

## 一、项目背景与核心问题

### 1.1 项目背景
在与众多小微制造企业客户的交流中，我们发现一个普遍现象：

> **"BOM=一张生产用的材料清单"** 的认知，严重限制了企业的管理效率与成本控制能力。

### 1.2 当前存在的核心问题

#### 🎯 问题一：BOM用途局限，只服务生产
- 忽略研发、工艺、采购、财务、售后等关键环节
- 数据无法在各部门间流通
- 信息孤岛严重，重复录入工作量大

#### 🎯 问题二：BOM版本管理混乱
- 客户定制或产品升级导致多个版本并存
- 历史版本难以追溯，变更责任不清
- 缺乏有效的版本控制机制

#### 🎯 问题三：BOM与财务、库存脱节
- 物料变更未同步成本核算
- 采购计划和库存策略滞后
- 造成成本失控与库存积压

#### 🎯 问题四：小微企业缺乏BOM专业知识
- EBOM、MBOM等术语陌生
- 传统系统门槛高，无法快速上手
- 缺乏专业的BOM管理人员

---

## 二、项目目标

### 2.1 总体目标
构建一套**轻量级、易用、低门槛**的BOM协同管理系统，帮助小微企业：

- 从"生产导向BOM"升级为"全流程驱动BOM"
- 实现**数据联动、成本可控、协同高效**
- 在**不理解专业术语**的情况下也能轻松使用

### 2.2 具体目标
1. **降低使用门槛**: 通过引导式设计和术语简化，让非专业人员也能快速上手
2. **提升协同效率**: 实现各部门间BOM数据的实时同步和协同工作
3. **增强成本控制**: 通过BOM与财务系统联动，实现实时成本核算和分析
4. **优化库存管理**: 基于BOM自动生成采购计划，减少库存积压
5. **支持业务扩展**: 满足客户定制、售后服务等多样化业务需求

---

## 三、系统功能模块设计

### 3.1 多类型BOM建模模块（术语引导式设计）

#### 功能描述
支持按业务场景创建不同类型的BOM，采用易懂的术语和引导式流程。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 统一BOM建模引擎 | 支持创建：设计用、工艺用、生产用、定制用、售后用BOM | 高 |
| 弱化术语设计 | 界面使用"产品结构""工艺流程用料"等易懂表达 | 高 |
| 引导式建模流程 | 采用"一步步引导填写"方式，降低建模难度 | 高 |
| BOM版本控制 | 支持多版本、变更记录、版本对比 | 中 |
| 快速导入导出 | 支持Excel模板导入、快速复制已有BOM | 高 |

#### 验收标准
- [ ] 支持5种不同业务场景的BOM类型
- [ ] 新用户可在30分钟内完成首个BOM建模
- [ ] 支持Excel一键导入，错误率<5%

### 3.2 产品设计用料（设计BOM）模块

#### 功能描述
管理产品的基础结构和用料信息，支持多级嵌套和替代件管理。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 产品结构建模 | 可视化建模，支持零件多级嵌套 | 高 |
| 替代件设置 | 设置可选用、可替代材料 | 中 |
| 历史版本管理 | 每次结构变更保存为新版本，支持查看变更记录 | 中 |

### 3.3 工艺流程用料（工艺BOM）模块

#### 功能描述
定义生产工艺路线和各工序的物料消耗。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 工艺路线设置 | 定义生产步骤、加工顺序 | 高 |
| 工序物料配置 | 每道工序消耗哪些材料 | 高 |
| 工艺版本管理 | 工艺随设计变更而调整，独立控制版本 | 中 |

### 3.4 生产执行用料（生产BOM）模块

#### 功能描述
基于生产订单自动生成生产用料清单，支持替代料管理。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 工单用料自动展开 | 基于生产订单生成生产BOM | 高 |
| 替代料提醒机制 | 支持设置优先级和替代规则 | 中 |
| 与库存/采购联动 | 自动生成物料需求清单MRP建议单 | 高 |

### 3.5 客户定制配置（销售BOM）模块

#### 功能描述
支持客户定制需求，生成个性化的产品配置。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 客户定制选项管理 | 针对不同客户需求生成配置型BOM | 中 |
| 与报价单/销售单对接 | 根据销售BOM自动生成报价单和后续工单 | 中 |

### 3.6 成本核算与财务联动模块

#### 功能描述
实现BOM与财务系统的深度集成，提供实时成本分析。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 实时成本计算 | 根据BOM材料+工艺计算标准成本 | 高 |
| 成本模拟分析 | 不同版本/不同物料组合对比成本影响 | 中 |
| 报表输出 | 产品成本报表、订单利润报表等 | 高 |

### 3.7 采购库存联动模块

#### 功能描述
基于BOM自动生成采购计划，优化库存结构。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 自动生成采购计划 | 基于BOM+现有库存，生成缺料清单 | 高 |
| 替代料库存优先机制 | 优先使用可替代库存，减少积压 | 中 |
| 库存结构优化分析 | 提供用料频率、呆滞材料建议 | 低 |

### 3.8 售后服务用料（服务BOM）模块

#### 功能描述
记录售后服务用料结构，支持快速维修定位。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 售后用料结构记录 | 根据交付记录自动生成Service BOM | 低 |
| 快速定位 | 通过产品SN编号查用料结构 | 低 |
| 替换件建议 | 可配置替代件路径，提升维修效率 | 低 |

### 3.9 协同与权限流程模块

#### 功能描述
支持多部门协同工作和权限管理。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| 部门协同流程 | 支持BOM创建、修改、审批的流程控制 | 中 |
| 通知机制 | 物料变更、BOM审批等自动推送消息 | 中 |
| 角色权限控制 | 不同角色只看可见数据，保障信息安全 | 高 |

### 3.10 BOM使用助手与教学模块

#### 功能描述
降低学习成本，提供使用指导和教学功能。

#### 主要功能
| 功能点 | 描述 | 优先级 |
|--------|------|--------|
| BOM引导助手 | 首次使用引导用户一步步完成建模 | 高 |
| 示例模板库 | 提供行业常见产品的BOM模板 | 中 |
| 术语解释系统 | 提供一键术语解释按钮，边用边学 | 中 |

---

## 四、用户故事与使用场景

### 4.1 用户角色定义
| 角色 | 职责描述 | 主要需求 |
|------|----------|----------|
| 研发工程师 | 负责产品设计和结构定义 | 建立产品基本结构，管理设计变更 |
| 工艺工程师 | 负责生产工艺设计 | 根据设计结构添加工艺路线 |
| 生产主管 | 负责生产计划和执行 | 快速生成生产用料清单 |
| 财务人员 | 负责成本核算和分析 | 实时获取成本变化信息 |
| 采购人员 | 负责物料采购 | 精确的采购需求和库存信息 |
| 售后人员 | 负责客户服务和维修 | 快速查找设备用料和替代件信息 |

### 4.2 典型使用场景

#### 场景一：新产品开发
**参与角色**: 研发工程师、工艺工程师、财务人员
**流程描述**:
1. 研发工程师创建设计BOM，定义产品结构和用料
2. 工艺工程师基于设计BOM创建工艺BOM，添加工艺路线
3. 财务人员查看成本分析，评估产品盈利能力
4. 系统自动生成初步的采购需求建议

#### 场景二：客户定制订单处理
**参与角色**: 销售人员、研发工程师、生产主管
**流程描述**:
1. 销售人员根据客户需求创建销售BOM
2. 系统基于标准BOM和定制要求生成定制BOM
3. 生产主管确认生产可行性和资源需求
4. 自动生成报价单和生产计划

#### 场景三：生产执行
**参与角色**: 生产主管、采购人员、仓库管理员
**流程描述**:
1. 生产主管根据订单生成生产BOM
2. 系统检查库存，自动生成缺料清单
3. 采购人员根据缺料清单进行采购
4. 仓库管理员按BOM进行物料配送

#### 场景四：售后维修
**参与角色**: 售后人员、仓库管理员
**流程描述**:
1. 售后人员通过产品SN查询服务BOM
2. 系统显示原始用料和可替代件信息
3. 确认维修用料需求和库存情况
4. 快速完成维修服务

---

## 五、非功能性需求

### 5.1 性能需求
| 指标 | 要求 | 说明 |
|------|------|------|
| 响应时间 | 页面加载<3秒，查询响应<2秒 | 保证用户体验流畅 |
| 并发用户数 | 支持100个并发用户 | 满足中小企业使用需求 |
| 数据处理能力 | 单个BOM支持1000个物料项 | 满足复杂产品需求 |
| 系统可用性 | 99.5%以上 | 保证业务连续性 |

### 5.2 安全需求
| 类别 | 要求 | 实现方式 |
|------|------|----------|
| 数据安全 | 敏感数据加密存储 | 数据库加密、传输加密 |
| 访问控制 | 基于角色的权限管理 | RBAC权限模型 |
| 操作审计 | 关键操作日志记录 | 操作日志、变更追踪 |
| 数据备份 | 每日自动备份 | 数据库备份、文件备份 |

### 5.3 兼容性需求
| 类别 | 要求 |
|------|------|
| 浏览器兼容 | Chrome 80+, Firefox 75+, Edge 80+, Safari 13+ |
| 移动端支持 | 支持平板和手机浏览器访问 |
| 数据格式 | 支持Excel、CSV格式导入导出 |
| 系统集成 | 提供标准API接口，支持与ERP系统集成 |

### 5.4 可用性需求
| 指标 | 要求 |
|------|------|
| 学习成本 | 新用户30分钟内完成基本操作 |
| 操作便捷性 | 常用功能不超过3次点击 |
| 错误处理 | 提供友好的错误提示和解决建议 |
| 帮助系统 | 提供在线帮助和操作指导 |

---

## 六、技术架构建议

### 6.1 总体架构
采用前后端分离的B/S架构，支持云部署和本地部署。

### 6.2 技术选型
| 层次 | 技术选择 | 说明 |
|------|----------|------|
| 前端框架 | Vue3 + Element Plus | 现代化UI框架，组件丰富 |
| 后端框架 | Spring Boot + MyBatis | 成熟稳定，开发效率高 |
| 数据库 | PostgreSQL | 开源免费，性能优秀 |
| 缓存 | Redis | 提升系统性能 |
| 文件存储 | 本地存储/阿里云OSS | 支持多种存储方式 |

### 6.3 架构特点
- **模块化设计**: 支持功能模块渐进启用
- **微服务友好**: 为未来扩展预留接口
- **移动端适配**: 响应式设计，支持多终端访问
- **API优先**: 提供完整的REST API

---

## 七、项目实施计划

### 7.1 阶段性交付计划

#### 阶段一：启动版（3个月）
**目标**: 实现核心BOM管理功能
**功能模块**:
- 设计BOM管理
- 生产BOM管理
- 基础成本分析
- Excel导入导出
- 用户权限管理

**验收标准**:
- [ ] 支持基本的BOM建模和管理
- [ ] 实现Excel数据导入，准确率>95%
- [ ] 完成用户培训，满意度>80%

#### 阶段二：协同版（2个月）
**目标**: 实现部门间协同和数据联动
**功能模块**:
- 工艺BOM管理
- 采购库存联动
- BOM版本控制
- 协同工作流程

**验收标准**:
- [ ] 实现BOM多版本管理
- [ ] 完成与采购、库存系统的数据联动
- [ ] 部门协同效率提升30%

#### 阶段三：增强版（2个月）
**目标**: 完善高级功能和业务扩展
**功能模块**:
- 销售BOM管理
- 服务BOM管理
- 高级成本分析
- 移动端支持

**验收标准**:
- [ ] 支持客户定制和售后服务场景
- [ ] 移动端功能完整可用
- [ ] 系统整体性能达标

### 7.2 里程碑计划
| 里程碑 | 时间节点 | 交付内容 |
|--------|----------|----------|
| 需求确认 | 第1个月末 | 详细需求规格书 |
| 原型验证 | 第2个月末 | 可演示的系统原型 |
| 启动版上线 | 第3个月末 | 基础功能可用的系统 |
| 协同版上线 | 第5个月末 | 支持协同的完整系统 |
| 增强版上线 | 第7个月末 | 功能完备的最终系统 |

---

## 八、验收标准

### 8.1 功能验收标准
| 验收项 | 验收标准 | 测试方法 |
|--------|----------|----------|
| BOM类型支持 | 支持5种BOM类型的独立管理与联动 | 功能测试 |
| 术语友好性 | 系统内所有专业术语均有易懂解释 | 用户体验测试 |
| 版本管理 | 所有BOM支持版本存档、对比、回滚 | 功能测试 |
| 成本计算 | 任一BOM可自动核算成本、生成报表 | 数据准确性测试 |
| 数据联动 | BOM变更能联动采购、库存、财务模块 | 集成测试 |
| 引导助手 | 新用户可通过向导完成首次建模 | 用户测试 |
| 数据导入 | 支持Excel导入，错误率<5% | 数据测试 |

### 8.2 性能验收标准
| 指标 | 标准值 | 测试条件 |
|------|--------|----------|
| 页面响应时间 | <3秒 | 正常网络环境 |
| 查询响应时间 | <2秒 | 1000条BOM数据 |
| 并发用户支持 | 100用户 | 压力测试 |
| 系统可用性 | >99.5% | 连续运行30天 |

### 8.3 用户满意度标准
| 指标 | 目标值 | 评估方式 |
|------|--------|----------|
| 用户满意度 | >85% | 用户调研问卷 |
| 学习成本 | <30分钟 | 新用户测试 |
| 操作效率提升 | >30% | 对比测试 |
| 系统稳定性评价 | >90%满意 | 用户反馈 |

---

## 九、风险分析与应对措施

### 9.1 技术风险
| 风险 | 影响程度 | 应对措施 |
|------|----------|----------|
| 数据迁移复杂 | 中 | 提供多种导入工具，分步迁移 |
| 系统集成困难 | 中 | 预留标准接口，提供集成文档 |
| 性能不达标 | 高 | 提前进行性能测试和优化 |

### 9.2 业务风险
| 风险 | 影响程度 | 应对措施 |
|------|----------|----------|
| 用户接受度低 | 高 | 加强用户培训，优化用户体验 |
| 需求变更频繁 | 中 | 采用敏捷开发，快速响应变更 |
| 竞争产品冲击 | 中 | 突出差异化优势，持续创新 |

### 9.3 项目风险
| 风险 | 影响程度 | 应对措施 |
|------|----------|----------|
| 开发进度延期 | 中 | 合理安排资源，设置缓冲时间 |
| 关键人员流失 | 高 | 知识文档化，团队备份 |
| 预算超支 | 中 | 严格控制范围，分阶段投入 |

---

## 十、项目成功标准

### 10.1 业务价值实现
- **效率提升**: BOM管理效率提升50%以上
- **成本控制**: 物料成本控制精度提升30%
- **库存优化**: 库存周转率提升20%
- **协同改善**: 部门间协同效率提升40%

### 10.2 用户满意度
- 用户满意度达到85%以上
- 用户活跃度达到80%以上
- 系统故障投诉率<5%

### 10.3 技术指标
- 系统稳定运行，可用性>99.5%
- 性能指标全部达标
- 安全事故零发生

---

## 十一、附录

### 11.1 术语对照表
| 专业术语 | 系统显示 | 说明 |
|----------|----------|------|
| EBOM | 设计用料清单 | 工程设计阶段的物料清单 |
| MBOM | 工艺用料清单 | 制造工艺阶段的物料清单 |
| PBOM | 生产用料清单 | 生产执行阶段的物料清单 |
| SBOM | 销售配置清单 | 销售定制阶段的物料清单 |
| Service BOM | 售后用料清单 | 售后服务阶段的物料清单 |

### 11.2 参考文档
- 《制造业BOM管理最佳实践》
- 《小微企业数字化转型指南》
- 《ERP系统集成标准》

---

**文档结束**

> **核心价值总结**: "一张表"解决不了企业的全流程问题。我们开发的BOM协同系统，用易懂的方式连接设计、生产、采购、财务、售后，让企业数据从一开始就统一，成本更清晰，库存更合理，效率更高，让小企业也能用得起真正的数字化。
