# BOM协同管理系统

一个专为小企业设计的BOM（物料清单）协同管理系统，让小企业也能用得起真正的数字化管理工具。

## 🚀 项目特色

- **多类型BOM管理**：支持设计BOM、工艺BOM、生产BOM、销售BOM、服务BOM
- **实时成本分析**：自动计算产品成本，支持多维度成本分析
- **智能采购计划**：基于BOM自动生成采购需求，优化库存管理
- **权限管理**：灵活的角色权限体系，适应不同部门需求
- **响应式设计**：支持PC和移动端访问

## 🛠️ 技术栈

- **前端框架**：Vue 3 + Composition API
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router 4
- **图表库**：ECharts
- **构建工具**：Vite
- **样式预处理**：Sass

## 📦 安装和运行

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

访问 http://localhost:3000

### 生产环境构建

```bash
npm run build
```

### 预览构建结果

```bash
npm run preview
```

## 👥 演示账号

系统提供了多个角色的演示账号，密码均为 `123456`：

| 角色 | 用户名 | 说明 |
|------|--------|------|
| 系统管理员 | admin | 拥有所有权限 |
| 研发工程师 | engineer | 负责设计和工艺BOM |
| 生产主管 | production | 负责生产BOM和库存 |
| 财务人员 | finance | 负责成本分析 |
| 采购人员 | purchase | 负责采购计划 |
| 销售人员 | sales | 负责客户定制 |
| 售后技师 | service | 负责维修服务 |

## 🏗️ 项目结构

```
src/
├── components/          # 公共组件
├── layout/             # 布局组件
├── mock/               # 模拟数据
├── router/             # 路由配置
├── stores/             # 状态管理
├── styles/             # 全局样式
├── utils/              # 工具函数
└── views/              # 页面组件
    ├── dashboard/      # 工作台
    ├── bom/           # BOM管理
    │   ├── design/    # 设计BOM
    │   ├── process/   # 工艺BOM
    │   ├── production/# 生产BOM
    │   ├── sales/     # 销售BOM
    │   └── service/   # 服务BOM
    ├── cost/          # 成本管理
    │   ├── analysis/  # 成本分析
    │   └── report/    # 成本报表
    ├── purchase/      # 采购管理
    │   └── plan/      # 采购计划
    ├── inventory/     # 库存管理
    │   └── analysis/  # 库存分析
    ├── system/        # 系统管理
    │   ├── user/      # 用户管理
    │   └── role/      # 角色管理
    └── login/         # 登录页面
```

## 🎯 功能模块

### BOM管理
- **设计BOM**：产品设计阶段的物料清单
- **工艺BOM**：制造工艺阶段的物料清单，包含工艺路线
- **生产BOM**：生产执行阶段的物料清单，支持库存检查
- **销售BOM**：客户定制配置，支持个性化产品配置
- **服务BOM**：售后服务用料，支持快速维修定位

### 成本管理
- **成本分析**：实时成本计算和多维度分析
- **成本报表**：生成各类成本报表，支持导出

### 采购管理
- **采购计划**：基于BOM自动生成采购需求
- **库存预警**：低库存自动提醒

### 库存管理
- **库存分析**：实时库存监控和分析
- **库存调整**：支持入库、出库、盘点调整

### 系统管理
- **用户管理**：用户账号管理
- **角色管理**：角色权限配置

## 🔧 开发说明

### 添加新页面

1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.js` 中添加路由配置
3. 如需权限控制，在路由meta中添加权限标识

### 添加新的模拟数据

在 `src/mock/` 目录下创建对应的数据文件，并在相关store中引用。

### 样式规范

- 使用 BEM 命名规范
- 公共样式放在 `src/styles/` 目录
- 组件样式使用 scoped

## 📝 待完善功能

- [ ] 物料主数据管理
- [ ] 供应商管理
- [ ] 工艺路线管理
- [ ] 生产计划管理
- [ ] 质量管理
- [ ] 报表导出功能
- [ ] 数据备份和恢复
- [ ] 移动端优化

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 邮箱：<EMAIL>
- 项目地址：https://github.com/your-username/bom-management-system

---

**让小企业也能用得起真正的数字化管理工具！**
