{"name": "bom-management-system", "version": "1.0.0", "description": "BOM协同管理系统前端", "private": true, "scripts": {"dev": "vite --host 0.0.0.0 --port 3000", "build": "vite build", "preview": "vite preview --host 0.0.0.0 --port 4173", "serve": "npm run preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "vue-echarts": "^6.6.1", "xlsx": "^0.18.5", "file-saver": "^2.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "vite": "^4.4.5", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "@vue/eslint-config-prettier": "^8.0.0", "prettier": "^3.0.0", "sass": "^1.64.1", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1"}}