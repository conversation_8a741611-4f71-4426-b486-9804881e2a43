// BOM模拟数据
export const mockBomData = [
  // 智能温湿度监测器 - 设计BOM
  {
    id: 'BOM001',
    code: 'EBOM-TH-001',
    name: '智能温湿度监测器',
    type: 'design', // 设计用料清单
    status: 'active',
    version: '1.2',
    description: '基于ESP32的智能温湿度监测设备，支持WiFi远程监控',
    creator: '张工程师',
    createTime: '2024-01-15T10:30:00Z',
    updateTime: '2024-02-20T14:15:00Z',
    totalCost: 156.85,
    materials: [
      {
        id: 'BM001',
        materialId: 'M002',
        materialCode: 'ESP32-WROOM-32',
        materialName: 'ESP32无线模块',
        quantity: 1,
        unit: '个',
        unitPrice: 18.50,
        totalPrice: 18.50,
        position: 'U1',
        alternatives: ['ESP32-WROOM-32D'],
        required: true,
        notes: '主控制器，负责数据处理和WiFi通信'
      },
      {
        id: 'BM002',
        materialId: 'M005',
        materialCode: 'DHT22',
        materialName: 'DHT22温湿度传感器',
        quantity: 1,
        unit: '个',
        unitPrice: 12.80,
        totalPrice: 12.80,
        position: 'U2',
        alternatives: ['SHT30'],
        required: true,
        notes: '温湿度检测传感器'
      },
      {
        id: 'BM003',
        materialId: 'M007',
        materialCode: 'SSD1306-128x64',
        materialName: 'SSD1306 OLED显示屏',
        quantity: 1,
        unit: '个',
        unitPrice: 15.60,
        totalPrice: 15.60,
        position: 'LCD1',
        alternatives: ['SH1106-128x64'],
        required: true,
        notes: '显示温湿度数据'
      },
      {
        id: 'BM004',
        materialId: 'M003',
        materialCode: 'AMS1117-3.3',
        materialName: 'AMS1117-3.3V稳压器',
        quantity: 1,
        unit: '个',
        unitPrice: 0.85,
        totalPrice: 0.85,
        position: 'U3',
        alternatives: ['LM1117-3.3'],
        required: true,
        notes: '电源稳压'
      },
      {
        id: 'BM005',
        materialId: 'M011',
        materialCode: 'USB-C-16P',
        materialName: 'USB Type-C连接器',
        quantity: 1,
        unit: '个',
        unitPrice: 3.20,
        totalPrice: 3.20,
        position: 'J1',
        alternatives: ['Micro-USB-5P'],
        required: true,
        notes: '电源输入接口'
      },
      {
        id: 'BM006',
        materialId: 'M013',
        materialCode: 'PCB-FR4-100x80',
        materialName: 'FR4双面PCB板',
        quantity: 1,
        unit: '片',
        unitPrice: 25.00,
        totalPrice: 25.00,
        position: '',
        alternatives: [],
        required: true,
        notes: '主电路板'
      },
      {
        id: 'BM007',
        materialId: 'M014',
        materialCode: 'CASE-ABS-120x80x30',
        materialName: 'ABS塑料外壳',
        quantity: 1,
        unit: '个',
        unitPrice: 12.50,
        totalPrice: 12.50,
        position: '',
        alternatives: ['CASE-PC-120x80x30'],
        required: true,
        notes: '产品外壳'
      },
      {
        id: 'BM008',
        materialId: 'M009',
        materialCode: 'C0805-10uF',
        materialName: '10uF贴片电容',
        quantity: 5,
        unit: '个',
        unitPrice: 0.12,
        totalPrice: 0.60,
        position: 'C1-C5',
        alternatives: ['C1206-10uF'],
        required: true,
        notes: '电源滤波电容'
      },
      {
        id: 'BM009',
        materialId: 'M010',
        materialCode: 'R0805-10K',
        materialName: '10KΩ贴片电阻',
        quantity: 8,
        unit: '个',
        unitPrice: 0.05,
        totalPrice: 0.40,
        position: 'R1-R8',
        alternatives: ['R0603-10K'],
        required: true,
        notes: '上拉电阻'
      },
      {
        id: 'BM010',
        materialId: 'M015',
        materialCode: 'LED-0805-RED',
        materialName: '0805红色LED',
        quantity: 3,
        unit: '个',
        unitPrice: 0.15,
        totalPrice: 0.45,
        position: 'LED1-LED3',
        alternatives: ['LED-0603-RED'],
        required: true,
        notes: '状态指示灯'
      }
    ]
  },

  // 智能温湿度监测器 - 工艺BOM
  {
    id: 'BOM002',
    code: 'MBOM-TH-001',
    name: '智能温湿度监测器',
    type: 'process', // 工艺用料清单
    status: 'active',
    version: '1.1',
    description: '智能温湿度监测器制造工艺用料清单',
    creator: '李工艺师',
    createTime: '2024-01-20T09:00:00Z',
    updateTime: '2024-02-15T16:30:00Z',
    totalCost: 168.25,
    processSteps: [
      {
        stepId: 'P001',
        stepName: 'SMT贴片',
        stepOrder: 1,
        description: '贴片元器件焊接',
        workstation: 'SMT产线',
        standardTime: 15, // 分钟
        materials: [
          { materialId: 'M002', quantity: 1, position: 'U1' },
          { materialId: 'M003', quantity: 1, position: 'U3' },
          { materialId: 'M009', quantity: 5, position: 'C1-C5' },
          { materialId: 'M010', quantity: 8, position: 'R1-R8' },
          { materialId: 'M015', quantity: 3, position: 'LED1-LED3' }
        ]
      },
      {
        stepId: 'P002',
        stepName: '插件焊接',
        stepOrder: 2,
        description: '插件元器件焊接',
        workstation: '手工焊接台',
        standardTime: 20,
        materials: [
          { materialId: 'M005', quantity: 1, position: 'U2' },
          { materialId: 'M007', quantity: 1, position: 'LCD1' },
          { materialId: 'M011', quantity: 1, position: 'J1' }
        ]
      },
      {
        stepId: 'P003',
        stepName: '功能测试',
        stepOrder: 3,
        description: '电路功能测试',
        workstation: '测试台',
        standardTime: 10,
        materials: []
      },
      {
        stepId: 'P004',
        stepName: '装配',
        stepOrder: 4,
        description: '装入外壳',
        workstation: '装配台',
        standardTime: 5,
        materials: [
          { materialId: 'M014', quantity: 1, position: '' }
        ]
      }
    ],
    materials: [
      // 继承设计BOM的物料清单，但可能有工艺损耗
      {
        id: 'PM001',
        materialId: 'M002',
        materialCode: 'ESP32-WROOM-32',
        materialName: 'ESP32无线模块',
        quantity: 1.05, // 考虑5%损耗
        unit: '个',
        unitPrice: 18.50,
        totalPrice: 19.43,
        lossRate: 0.05,
        processStep: 'P001'
      }
      // ... 其他物料类似
    ]
  },

  // 智能温湿度监测器 - 生产BOM
  {
    id: 'BOM003',
    code: 'PBOM-TH-001-20240301',
    name: '智能温湿度监测器-生产订单001',
    type: 'production', // 生产用料清单
    status: 'active',
    version: '1.0',
    description: '生产订单PO-20240301的生产用料清单，数量100台',
    creator: '李主管',
    createTime: '2024-03-01T08:00:00Z',
    updateTime: '2024-03-01T08:00:00Z',
    productionOrder: 'PO-20240301',
    productionQty: 100,
    totalCost: 16825.00,
    materials: [
      {
        id: 'PBM001',
        materialId: 'M002',
        materialCode: 'ESP32-WROOM-32',
        materialName: 'ESP32无线模块',
        requiredQty: 105, // 100台 + 5%损耗
        availableQty: 200, // 库存数量
        shortageQty: 0, // 缺料数量
        unit: '个',
        unitPrice: 18.50,
        totalPrice: 1942.50,
        supplier: '乐鑫科技',
        leadTime: 10,
        status: 'sufficient' // sufficient/shortage/ordered
      },
      {
        id: 'PBM002',
        materialId: 'M005',
        materialCode: 'DHT22',
        materialName: 'DHT22温湿度传感器',
        requiredQty: 105,
        availableQty: 80,
        shortageQty: 25,
        unit: '个',
        unitPrice: 12.80,
        totalPrice: 1344.00,
        supplier: '奥松电子',
        leadTime: 8,
        status: 'shortage'
      }
      // ... 其他物料
    ]
  },

  // 多功能开发板 - 设计BOM
  {
    id: 'BOM004',
    code: 'EBOM-DEV-001',
    name: '多功能开发板',
    type: 'design',
    status: 'active',
    version: '2.0',
    description: '基于STM32F407的多功能开发板',
    creator: '王工程师',
    createTime: '2024-02-01T14:00:00Z',
    updateTime: '2024-02-25T11:20:00Z',
    totalCost: 285.60,
    materials: [
      {
        id: 'BM011',
        materialId: 'M001',
        materialCode: 'STM32F407VGT6',
        materialName: 'STM32F407VGT6微控制器',
        quantity: 1,
        unit: '个',
        unitPrice: 45.80,
        totalPrice: 45.80,
        position: 'U1',
        alternatives: ['STM32F405VGT6'],
        required: true,
        notes: '主控制器'
      },
      {
        id: 'BM012',
        materialId: 'M006',
        materialCode: 'MPU6050',
        materialName: 'MPU6050六轴传感器',
        quantity: 1,
        unit: '个',
        unitPrice: 8.90,
        totalPrice: 8.90,
        position: 'U2',
        alternatives: ['ICM20602'],
        required: true,
        notes: '惯性传感器'
      },
      {
        id: 'BM013',
        materialId: 'M008',
        materialCode: 'TFT-2.4-240x320',
        materialName: '2.4寸TFT彩色显示屏',
        quantity: 1,
        unit: '个',
        unitPrice: 28.90,
        totalPrice: 28.90,
        position: 'LCD1',
        alternatives: ['ILI9341-2.4'],
        required: true,
        notes: '彩色显示屏'
      },
      {
        id: 'BM014',
        materialId: 'M004',
        materialCode: 'MP2307DN',
        materialName: 'MP2307DN降压转换器',
        quantity: 2,
        unit: '个',
        unitPrice: 2.30,
        totalPrice: 4.60,
        position: 'U3,U4',
        alternatives: ['LM2596S'],
        required: true,
        notes: '电源管理'
      },
      {
        id: 'BM015',
        materialId: 'M016',
        materialCode: 'CRYSTAL-8MHz',
        materialName: '8MHz晶振',
        quantity: 1,
        unit: '个',
        unitPrice: 1.20,
        totalPrice: 1.20,
        position: 'Y1',
        alternatives: ['CRYSTAL-16MHz'],
        required: true,
        notes: '系统时钟'
      }
      // ... 更多物料
    ]
  }
]

// BOM类型定义
export const bomTypes = [
  { value: 'design', label: '设计用料清单', description: '产品设计阶段的物料清单，定义产品基本结构' },
  { value: 'process', label: '工艺用料清单', description: '制造工艺阶段的物料清单，包含工艺路线和损耗' },
  { value: 'production', label: '生产用料清单', description: '生产执行阶段的物料清单，用于实际生产' },
  { value: 'sales', label: '销售配置清单', description: '销售定制阶段的物料清单，支持客户定制' },
  { value: 'service', label: '售后用料清单', description: '售后服务阶段的物料清单，用于维修和更换' }
]

// BOM状态定义
export const bomStatus = [
  { value: 'draft', label: '草稿', color: '#909399' },
  { value: 'review', label: '审核中', color: '#E6A23C' },
  { value: 'active', label: '生效', color: '#67C23A' },
  { value: 'obsolete', label: '废弃', color: '#F56C6C' }
]
