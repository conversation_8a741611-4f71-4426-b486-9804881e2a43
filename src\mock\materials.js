// 电子产品物料模拟数据
export const mockMaterials = [
  // 主控芯片类
  {
    id: 'M001',
    code: 'STM32F407VGT6',
    name: 'STM32F407VGT6微控制器',
    category: '主控芯片',
    subcategory: 'ARM Cortex-M4',
    unit: '个',
    price: 45.80,
    supplier: '意法半导体',
    leadTime: 15,
    minOrderQty: 100,
    stockQty: 500,
    safetyStock: 50,
    description: '32位ARM Cortex-M4内核，168MHz，1MB Flash，192KB RAM',
    specifications: {
      package: 'LQFP-100',
      voltage: '1.8V-3.6V',
      temperature: '-40°C to +85°C',
      flash: '1MB',
      ram: '192KB'
    },
    alternatives: ['STM32F405VGT6', 'STM32F407VET6']
  },
  {
    id: 'M002',
    code: 'ESP32-WROOM-32',
    name: 'ESP32无线模块',
    category: '无线通信',
    subcategory: 'WiFi/蓝牙模块',
    unit: '个',
    price: 18.50,
    supplier: '乐鑫科技',
    leadTime: 10,
    minOrderQty: 50,
    stockQty: 200,
    safetyStock: 20,
    description: 'WiFi + 蓝牙双模无线通信模块',
    specifications: {
      package: 'SMD',
      voltage: '3.0V-3.6V',
      frequency: '2.4GHz',
      range: '100m',
      power: '20dBm'
    },
    alternatives: ['ESP32-WROOM-32D', 'ESP32-WROOM-32U']
  },
  
  // 电源管理类
  {
    id: 'M003',
    code: 'AMS1117-3.3',
    name: 'AMS1117-3.3V稳压器',
    category: '电源管理',
    subcategory: '线性稳压器',
    unit: '个',
    price: 0.85,
    supplier: '先进半导体',
    leadTime: 7,
    minOrderQty: 1000,
    stockQty: 5000,
    safetyStock: 500,
    description: '3.3V输出线性稳压器，最大输出电流1A',
    specifications: {
      package: 'SOT-223',
      inputVoltage: '4.5V-15V',
      outputVoltage: '3.3V',
      outputCurrent: '1A',
      dropout: '1.2V'
    },
    alternatives: ['LM1117-3.3', 'SPX1117M3-L-3-3']
  },
  {
    id: 'M004',
    code: 'MP2307DN',
    name: 'MP2307DN降压转换器',
    category: '电源管理',
    subcategory: '开关稳压器',
    unit: '个',
    price: 2.30,
    supplier: '芯源系统',
    leadTime: 12,
    minOrderQty: 500,
    stockQty: 1000,
    safetyStock: 100,
    description: '3A同步整流降压转换器',
    specifications: {
      package: 'SOIC-8',
      inputVoltage: '4.5V-23V',
      outputCurrent: '3A',
      frequency: '340kHz',
      efficiency: '95%'
    },
    alternatives: ['LM2596S', 'XL4015E1']
  },
  
  // 传感器类
  {
    id: 'M005',
    code: 'DHT22',
    name: 'DHT22温湿度传感器',
    category: '传感器',
    subcategory: '温湿度传感器',
    unit: '个',
    price: 12.80,
    supplier: '奥松电子',
    leadTime: 8,
    minOrderQty: 100,
    stockQty: 300,
    safetyStock: 30,
    description: '数字温湿度传感器，高精度',
    specifications: {
      package: 'DIP-4',
      voltage: '3.3V-5.5V',
      temperatureRange: '-40°C to +80°C',
      humidityRange: '0-100%RH',
      accuracy: '±0.5°C, ±2%RH'
    },
    alternatives: ['SHT30', 'AM2302']
  },
  {
    id: 'M006',
    code: 'MPU6050',
    name: 'MPU6050六轴传感器',
    category: '传感器',
    subcategory: '惯性传感器',
    unit: '个',
    price: 8.90,
    supplier: 'InvenSense',
    leadTime: 14,
    minOrderQty: 200,
    stockQty: 400,
    safetyStock: 40,
    description: '三轴陀螺仪+三轴加速度计',
    specifications: {
      package: 'QFN-24',
      voltage: '2.375V-3.46V',
      interface: 'I2C',
      gyroRange: '±250°/s to ±2000°/s',
      accelRange: '±2g to ±16g'
    },
    alternatives: ['ICM20602', 'LSM6DS3']
  },
  
  // 显示器件类
  {
    id: 'M007',
    code: 'SSD1306-128x64',
    name: 'SSD1306 OLED显示屏',
    category: '显示器件',
    subcategory: 'OLED显示屏',
    unit: '个',
    price: 15.60,
    supplier: '中景园电子',
    leadTime: 10,
    minOrderQty: 50,
    stockQty: 150,
    safetyStock: 15,
    description: '0.96寸128x64像素OLED显示屏',
    specifications: {
      size: '0.96inch',
      resolution: '128x64',
      interface: 'I2C/SPI',
      voltage: '3.3V-5V',
      color: '蓝色/白色'
    },
    alternatives: ['SH1106-128x64', 'SSD1315-128x64']
  },
  {
    id: 'M008',
    code: 'TFT-2.4-240x320',
    name: '2.4寸TFT彩色显示屏',
    category: '显示器件',
    subcategory: 'TFT显示屏',
    unit: '个',
    price: 28.90,
    supplier: '奥松电子',
    leadTime: 12,
    minOrderQty: 20,
    stockQty: 80,
    safetyStock: 8,
    description: '2.4寸240x320像素TFT彩色显示屏',
    specifications: {
      size: '2.4inch',
      resolution: '240x320',
      interface: 'SPI',
      voltage: '3.3V-5V',
      backlight: 'LED'
    },
    alternatives: ['ILI9341-2.4', 'ST7789-2.4']
  },
  
  // 被动器件类
  {
    id: 'M009',
    code: 'C0805-10uF',
    name: '10uF贴片电容',
    category: '被动器件',
    subcategory: '电容',
    unit: '个',
    price: 0.12,
    supplier: '三星电机',
    leadTime: 5,
    minOrderQty: 5000,
    stockQty: 20000,
    safetyStock: 2000,
    description: '0805封装10uF陶瓷电容',
    specifications: {
      package: '0805',
      capacitance: '10uF',
      voltage: '25V',
      tolerance: '±10%',
      material: 'X7R'
    },
    alternatives: ['C0805-10uF-16V', 'C1206-10uF']
  },
  {
    id: 'M010',
    code: 'R0805-10K',
    name: '10KΩ贴片电阻',
    category: '被动器件',
    subcategory: '电阻',
    unit: '个',
    price: 0.05,
    supplier: '厚声电子',
    leadTime: 3,
    minOrderQty: 10000,
    stockQty: 50000,
    safetyStock: 5000,
    description: '0805封装10KΩ贴片电阻',
    specifications: {
      package: '0805',
      resistance: '10KΩ',
      power: '1/8W',
      tolerance: '±1%',
      temperature: '±100ppm/°C'
    },
    alternatives: ['R0603-10K', 'R1206-10K']
  },
  
  // 连接器类
  {
    id: 'M011',
    code: 'USB-C-16P',
    name: 'USB Type-C连接器',
    category: '连接器',
    subcategory: 'USB连接器',
    unit: '个',
    price: 3.20,
    supplier: '立讯精密',
    leadTime: 8,
    minOrderQty: 100,
    stockQty: 500,
    safetyStock: 50,
    description: 'USB Type-C 16Pin母座连接器',
    specifications: {
      pins: '16Pin',
      current: '3A',
      voltage: '20V',
      insertions: '10000次',
      mounting: 'SMT'
    },
    alternatives: ['USB-C-24P', 'Micro-USB-5P']
  },
  {
    id: 'M012',
    code: 'PH2.0-4P',
    name: 'PH2.0-4P连接器',
    category: '连接器',
    subcategory: '端子连接器',
    unit: '个',
    price: 0.80,
    supplier: 'JST',
    leadTime: 6,
    minOrderQty: 1000,
    stockQty: 3000,
    safetyStock: 300,
    description: 'PH2.0间距4Pin连接器',
    specifications: {
      pitch: '2.0mm',
      pins: '4Pin',
      current: '2A',
      voltage: '250V',
      material: 'PA66'
    },
    alternatives: ['XH2.54-4P', 'PH2.0-3P']
  },
  
  // 机械结构件
  {
    id: 'M013',
    code: 'PCB-FR4-100x80',
    name: 'FR4双面PCB板',
    category: '机械结构',
    subcategory: 'PCB板',
    unit: '片',
    price: 25.00,
    supplier: '嘉立创',
    leadTime: 7,
    minOrderQty: 5,
    stockQty: 50,
    safetyStock: 5,
    description: '100x80mm FR4双面PCB板',
    specifications: {
      size: '100x80mm',
      layers: '2层',
      thickness: '1.6mm',
      material: 'FR4',
      surface: '喷锡'
    },
    alternatives: ['PCB-FR4-100x80-4L', 'PCB-FR4-120x80']
  },
  {
    id: 'M014',
    code: 'CASE-ABS-120x80x30',
    name: 'ABS塑料外壳',
    category: '机械结构',
    subcategory: '外壳',
    unit: '个',
    price: 12.50,
    supplier: '塑胶制品厂',
    leadTime: 15,
    minOrderQty: 100,
    stockQty: 200,
    safetyStock: 20,
    description: '120x80x30mm ABS塑料外壳',
    specifications: {
      size: '120x80x30mm',
      material: 'ABS',
      color: '黑色',
      weight: '50g',
      mounting: '螺丝固定'
    },
    alternatives: ['CASE-PC-120x80x30', 'CASE-ABS-100x70x25']
  },
  
  // 辅助器件
  {
    id: 'M015',
    code: 'LED-0805-RED',
    name: '0805红色LED',
    category: '光电器件',
    subcategory: 'LED',
    unit: '个',
    price: 0.15,
    supplier: '亿光电子',
    leadTime: 5,
    minOrderQty: 2000,
    stockQty: 10000,
    safetyStock: 1000,
    description: '0805封装红色LED指示灯',
    specifications: {
      package: '0805',
      color: '红色',
      voltage: '2.0V',
      current: '20mA',
      luminosity: '150mcd'
    },
    alternatives: ['LED-0603-RED', 'LED-0805-GREEN']
  },
  {
    id: 'M016',
    code: 'CRYSTAL-8MHz',
    name: '8MHz晶振',
    category: '时钟器件',
    subcategory: '晶振',
    unit: '个',
    price: 1.20,
    supplier: '泰艺电子',
    leadTime: 10,
    minOrderQty: 500,
    stockQty: 1000,
    safetyStock: 100,
    description: '8MHz无源晶振',
    specifications: {
      frequency: '8MHz',
      package: 'HC-49S',
      tolerance: '±20ppm',
      temperature: '-20°C to +70°C',
      load: '18pF'
    },
    alternatives: ['CRYSTAL-16MHz', 'CRYSTAL-12MHz']
  }
]

// 物料分类
export const materialCategories = [
  {
    id: '1',
    name: '主控芯片',
    subcategories: ['ARM Cortex-M4', 'ARM Cortex-M3', 'ARM Cortex-M0', '8051', 'PIC']
  },
  {
    id: '2',
    name: '无线通信',
    subcategories: ['WiFi/蓝牙模块', 'LoRa模块', 'NB-IoT模块', '2G/3G/4G模块']
  },
  {
    id: '3',
    name: '电源管理',
    subcategories: ['线性稳压器', '开关稳压器', '电池管理', '充电芯片']
  },
  {
    id: '4',
    name: '传感器',
    subcategories: ['温湿度传感器', '惯性传感器', '压力传感器', '光学传感器']
  },
  {
    id: '5',
    name: '显示器件',
    subcategories: ['OLED显示屏', 'TFT显示屏', 'LCD显示屏', '数码管']
  },
  {
    id: '6',
    name: '被动器件',
    subcategories: ['电阻', '电容', '电感', '磁珠']
  },
  {
    id: '7',
    name: '连接器',
    subcategories: ['USB连接器', '端子连接器', '排针排母', '射频连接器']
  },
  {
    id: '8',
    name: '机械结构',
    subcategories: ['PCB板', '外壳', '散热器', '紧固件']
  },
  {
    id: '9',
    name: '光电器件',
    subcategories: ['LED', '光电二极管', '激光器', '光耦']
  },
  {
    id: '10',
    name: '时钟器件',
    subcategories: ['晶振', '晶体振荡器', 'RTC芯片']
  }
]

// 供应商信息
export const mockSuppliers = [
  {
    id: 'S001',
    name: '意法半导体',
    code: 'ST',
    contact: '张经理',
    phone: '021-12345678',
    email: '<EMAIL>',
    address: '上海市浦东新区',
    paymentTerms: '月结30天',
    deliveryTerms: 'EXW',
    rating: 'A'
  },
  {
    id: 'S002',
    name: '乐鑫科技',
    code: 'ESP',
    contact: '李工程师',
    phone: '0755-87654321',
    email: '<EMAIL>',
    address: '深圳市南山区',
    paymentTerms: '月结45天',
    deliveryTerms: 'FOB',
    rating: 'A'
  },
  {
    id: 'S003',
    name: '先进半导体',
    code: 'AMS',
    contact: '王销售',
    phone: '0512-98765432',
    email: '<EMAIL>',
    address: '苏州市工业园区',
    paymentTerms: '月结60天',
    deliveryTerms: 'CIF',
    rating: 'B'
  },
  {
    id: 'S004',
    name: '芯源系统',
    code: 'MPS',
    contact: '赵代理',
    phone: '010-11223344',
    email: '<EMAIL>',
    address: '北京市海淀区',
    paymentTerms: '现金',
    deliveryTerms: 'EXW',
    rating: 'A'
  },
  {
    id: 'S005',
    name: '嘉立创',
    code: 'JLC',
    contact: '刘客服',
    phone: '0755-83456789',
    email: '<EMAIL>',
    address: '深圳市宝安区',
    paymentTerms: '预付款',
    deliveryTerms: 'EXW',
    rating: 'A'
  }
]
