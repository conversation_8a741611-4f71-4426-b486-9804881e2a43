// 模拟用户数据
export const mockUsers = [
  {
    id: '1',
    username: 'admin',
    password: '123456',
    name: '系统管理员',
    email: '<EMAIL>',
    role: 'admin',
    department: '信息技术部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: ['*'] // 所有权限
  },
  {
    id: '2',
    username: 'engineer',
    password: '123456',
    name: '张工程师',
    email: '<EMAIL>',
    role: 'engineer',
    department: '研发部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'bom:design:view',
      'bom:design:create',
      'bom:design:edit',
      'bom:process:view',
      'bom:process:create',
      'bom:process:edit',
      'cost:analysis:view'
    ]
  },
  {
    id: '3',
    username: 'production',
    password: '123456',
    name: '李主管',
    email: '<EMAIL>',
    role: 'production_manager',
    department: '生产部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'bom:production:view',
      'bom:production:create',
      'bom:production:edit',
      'bom:process:view',
      'purchase:plan:view',
      'inventory:analysis:view'
    ]
  },
  {
    id: '4',
    username: 'finance',
    password: '123456',
    name: '王会计',
    email: '<EMAIL>',
    role: 'finance',
    department: '财务部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'cost:analysis:view',
      'cost:report:view',
      'cost:report:export',
      'bom:design:view',
      'bom:production:view'
    ]
  },
  {
    id: '5',
    username: 'purchase',
    password: '123456',
    name: '赵采购',
    email: '<EMAIL>',
    role: 'purchaser',
    department: '采购部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'purchase:plan:view',
      'purchase:plan:create',
      'purchase:plan:edit',
      'inventory:analysis:view',
      'bom:production:view'
    ]
  },
  {
    id: '6',
    username: 'sales',
    password: '123456',
    name: '刘销售',
    email: '<EMAIL>',
    role: 'sales',
    department: '销售部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'bom:sales:view',
      'bom:sales:create',
      'bom:sales:edit',
      'cost:analysis:view'
    ]
  },
  {
    id: '7',
    username: 'service',
    password: '123456',
    name: '陈技师',
    email: '<EMAIL>',
    role: 'service_engineer',
    department: '售后服务部',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    permissions: [
      'bom:service:view',
      'bom:service:create',
      'bom:service:edit',
      'bom:design:view'
    ]
  }
]

// 角色定义
export const mockRoles = [
  {
    id: '1',
    name: 'admin',
    displayName: '系统管理员',
    description: '拥有系统所有权限',
    permissions: ['*']
  },
  {
    id: '2',
    name: 'engineer',
    displayName: '研发工程师',
    description: '负责产品设计和工艺开发',
    permissions: [
      'bom:design:view',
      'bom:design:create',
      'bom:design:edit',
      'bom:process:view',
      'bom:process:create',
      'bom:process:edit',
      'cost:analysis:view'
    ]
  },
  {
    id: '3',
    name: 'production_manager',
    displayName: '生产主管',
    description: '负责生产计划和执行',
    permissions: [
      'bom:production:view',
      'bom:production:create',
      'bom:production:edit',
      'bom:process:view',
      'purchase:plan:view',
      'inventory:analysis:view'
    ]
  },
  {
    id: '4',
    name: 'finance',
    displayName: '财务人员',
    description: '负责成本核算和分析',
    permissions: [
      'cost:analysis:view',
      'cost:report:view',
      'cost:report:export',
      'bom:design:view',
      'bom:production:view'
    ]
  },
  {
    id: '5',
    name: 'purchaser',
    displayName: '采购人员',
    description: '负责物料采购',
    permissions: [
      'purchase:plan:view',
      'purchase:plan:create',
      'purchase:plan:edit',
      'inventory:analysis:view',
      'bom:production:view'
    ]
  },
  {
    id: '6',
    name: 'sales',
    displayName: '销售人员',
    description: '负责客户定制和销售',
    permissions: [
      'bom:sales:view',
      'bom:sales:create',
      'bom:sales:edit',
      'cost:analysis:view'
    ]
  },
  {
    id: '7',
    name: 'service_engineer',
    displayName: '售后工程师',
    description: '负责售后服务和维修',
    permissions: [
      'bom:service:view',
      'bom:service:create',
      'bom:service:edit',
      'bom:design:view'
    ]
  }
]
