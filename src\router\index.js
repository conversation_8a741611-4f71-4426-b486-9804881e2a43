import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

// 布局组件
const Layout = () => import('@/layout/index.vue')

// 页面组件
const Login = () => import('@/views/login/index.vue')
const Dashboard = () => import('@/views/dashboard/index.vue')

// BOM相关页面
const BomDesign = () => import('@/views/bom/design/index.vue')
const BomProcess = () => import('@/views/bom/process/index.vue')
const BomProduction = () => import('@/views/bom/production/index.vue')
const BomSales = () => import('@/views/bom/sales/index.vue')
const BomService = () => import('@/views/bom/service/index.vue')

// 成本管理
const CostAnalysis = () => import('@/views/cost/analysis/index.vue')
const CostReport = () => import('@/views/cost/report/index.vue')

// 采购库存
const PurchasePlan = () => import('@/views/purchase/plan/index.vue')
const InventoryAnalysis = () => import('@/views/inventory/analysis/index.vue')

// 系统管理
const UserManagement = () => import('@/views/system/user/index.vue')
const RoleManagement = () => import('@/views/system/role/index.vue')

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: '登录', requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '工作台', icon: 'House' }
      }
    ]
  },
  {
    path: '/bom',
    component: Layout,
    meta: { title: 'BOM管理', icon: 'Document' },
    children: [
      {
        path: 'design',
        name: 'BomDesign',
        component: BomDesign,
        meta: { title: '设计用料清单', icon: 'Edit' }
      },
      {
        path: 'process',
        name: 'BomProcess',
        component: BomProcess,
        meta: { title: '工艺用料清单', icon: 'Setting' }
      },
      {
        path: 'production',
        name: 'BomProduction',
        component: BomProduction,
        meta: { title: '生产用料清单', icon: 'Operation' }
      },
      {
        path: 'sales',
        name: 'BomSales',
        component: BomSales,
        meta: { title: '销售配置清单', icon: 'ShoppingCart' }
      },
      {
        path: 'service',
        name: 'BomService',
        component: BomService,
        meta: { title: '售后用料清单', icon: 'Tools' }
      }
    ]
  },
  {
    path: '/cost',
    component: Layout,
    meta: { title: '成本管理', icon: 'Money' },
    children: [
      {
        path: 'analysis',
        name: 'CostAnalysis',
        component: CostAnalysis,
        meta: { title: '成本分析', icon: 'TrendCharts' }
      },
      {
        path: 'report',
        name: 'CostReport',
        component: CostReport,
        meta: { title: '成本报表', icon: 'Document' }
      }
    ]
  },
  {
    path: '/purchase',
    component: Layout,
    meta: { title: '采购管理', icon: 'ShoppingBag' },
    children: [
      {
        path: 'plan',
        name: 'PurchasePlan',
        component: PurchasePlan,
        meta: { title: '采购计划', icon: 'Calendar' }
      }
    ]
  },
  {
    path: '/inventory',
    component: Layout,
    meta: { title: '库存管理', icon: 'Box' },
    children: [
      {
        path: 'analysis',
        name: 'InventoryAnalysis',
        component: InventoryAnalysis,
        meta: { title: '库存分析', icon: 'DataAnalysis' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    meta: { title: '系统管理', icon: 'Setting' },
    children: [
      {
        path: 'user',
        name: 'UserManagement',
        component: UserManagement,
        meta: { title: '用户管理', icon: 'User' }
      },
      {
        path: 'role',
        name: 'RoleManagement',
        component: RoleManagement,
        meta: { title: '角色管理', icon: 'UserFilled' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth !== false && !userStore.isLoggedIn) {
    next('/login')
  } else if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
  } else {
    next()
  }
})

export default router
