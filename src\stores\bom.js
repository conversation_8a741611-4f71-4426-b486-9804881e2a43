import { defineStore } from 'pinia'
import { ref } from 'vue'
import { mockBomData } from '@/mock/bom'

export const useBomStore = defineStore('bom', () => {
  const bomList = ref([])
  const currentBom = ref(null)
  const bomVersions = ref([])
  
  // 获取BOM列表
  const getBomList = async (params = {}) => {
    try {
      // 模拟API调用
      let data = [...mockBomData]
      
      // 根据参数过滤
      if (params.type) {
        data = data.filter(item => item.type === params.type)
      }
      if (params.status) {
        data = data.filter(item => item.status === params.status)
      }
      if (params.keyword) {
        data = data.filter(item => 
          item.name.includes(params.keyword) || 
          item.code.includes(params.keyword)
        )
      }
      
      bomList.value = data
      return { success: true, data }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 获取BOM详情
  const getBomDetail = async (id) => {
    try {
      const bom = mockBomData.find(item => item.id === id)
      if (!bom) {
        throw new Error('BOM不存在')
      }
      currentBom.value = bom
      return { success: true, data: bom }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 创建BOM
  const createBom = async (bomData) => {
    try {
      const newBom = {
        id: Date.now().toString(),
        ...bomData,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        version: '1.0',
        status: 'draft'
      }
      
      mockBomData.push(newBom)
      bomList.value.push(newBom)
      
      return { success: true, data: newBom }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 更新BOM
  const updateBom = async (id, bomData) => {
    try {
      const index = mockBomData.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('BOM不存在')
      }
      
      const updatedBom = {
        ...mockBomData[index],
        ...bomData,
        updateTime: new Date().toISOString()
      }
      
      mockBomData[index] = updatedBom
      
      const listIndex = bomList.value.findIndex(item => item.id === id)
      if (listIndex !== -1) {
        bomList.value[listIndex] = updatedBom
      }
      
      if (currentBom.value && currentBom.value.id === id) {
        currentBom.value = updatedBom
      }
      
      return { success: true, data: updatedBom }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 删除BOM
  const deleteBom = async (id) => {
    try {
      const index = mockBomData.findIndex(item => item.id === id)
      if (index === -1) {
        throw new Error('BOM不存在')
      }
      
      mockBomData.splice(index, 1)
      
      const listIndex = bomList.value.findIndex(item => item.id === id)
      if (listIndex !== -1) {
        bomList.value.splice(listIndex, 1)
      }
      
      return { success: true }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 复制BOM
  const copyBom = async (id, newName) => {
    try {
      const originalBom = mockBomData.find(item => item.id === id)
      if (!originalBom) {
        throw new Error('原BOM不存在')
      }
      
      const newBom = {
        ...originalBom,
        id: Date.now().toString(),
        name: newName,
        code: `${originalBom.code}_COPY`,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        version: '1.0',
        status: 'draft'
      }
      
      mockBomData.push(newBom)
      bomList.value.push(newBom)
      
      return { success: true, data: newBom }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 获取BOM版本历史
  const getBomVersions = async (bomId) => {
    try {
      // 模拟版本数据
      const versions = [
        {
          id: '1',
          version: '1.0',
          description: '初始版本',
          createTime: '2024-01-01 10:00:00',
          creator: '张工程师',
          status: 'active'
        },
        {
          id: '2',
          version: '1.1',
          description: '优化物料配置',
          createTime: '2024-01-15 14:30:00',
          creator: '李工程师',
          status: 'draft'
        }
      ]
      
      bomVersions.value = versions
      return { success: true, data: versions }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  return {
    bomList,
    currentBom,
    bomVersions,
    getBomList,
    getBomDetail,
    createBom,
    updateBom,
    deleteBom,
    copyBom,
    getBomVersions
  }
})
