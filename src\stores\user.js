import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { mockUsers } from '@/mock/user'

export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('token') || '')
  
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  
  // 登录
  const login = async (loginForm) => {
    try {
      // 模拟登录验证
      const user = mockUsers.find(u => 
        u.username === loginForm.username && u.password === loginForm.password
      )
      
      if (!user) {
        throw new Error('用户名或密码错误')
      }
      
      // 模拟token
      const mockToken = `mock_token_${user.id}_${Date.now()}`
      
      token.value = mockToken
      userInfo.value = {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role,
        department: user.department,
        avatar: user.avatar,
        permissions: user.permissions
      }
      
      localStorage.setItem('token', mockToken)
      localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
      
      return { success: true, data: userInfo.value }
    } catch (error) {
      return { success: false, message: error.message }
    }
  }
  
  // 登出
  const logout = () => {
    token.value = ''
    userInfo.value = null
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')
  }
  
  // 初始化用户信息
  const initUser = () => {
    const savedUserInfo = localStorage.getItem('userInfo')
    if (savedUserInfo && token.value) {
      try {
        userInfo.value = JSON.parse(savedUserInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    if (!userInfo.value || !userInfo.value.permissions) return false
    return userInfo.value.permissions.includes(permission) || userInfo.value.permissions.includes('*')
  }
  
  // 检查角色
  const hasRole = (role) => {
    if (!userInfo.value) return false
    return userInfo.value.role === role
  }
  
  return {
    userInfo,
    token,
    isLoggedIn,
    login,
    logout,
    initUser,
    hasPermission,
    hasRole
  }
})
