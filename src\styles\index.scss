// 全局样式文件

// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// 布局相关
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  flex-shrink: 0;
}

.layout-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.layout-sidebar {
  flex-shrink: 0;
  width: 200px;
  transition: width 0.3s;
  
  &.collapsed {
    width: 64px;
  }
}

.layout-content {
  flex: 1;
  padding: 20px;
  overflow: auto;
  background-color: #f5f5f5;
}

// 卡片样式
.content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  
  .card-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  
  .card-body {
    padding: 20px;
  }
}

// 表格样式
.table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .table-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
    
    .table-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 表单样式
.form-container {
  .form-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

// 工具类
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-center { display: flex; justify-content: center; align-items: center; }
.flex-between { display: flex; justify-content: space-between; align-items: center; }

.mb-16 { margin-bottom: 16px; }
.mb-20 { margin-bottom: 20px; }
.mb-24 { margin-bottom: 24px; }

.p-16 { padding: 16px; }
.p-20 { padding: 20px; }
.p-24 { padding: 24px; }

// 状态颜色
.status-success { color: #67c23a; }
.status-warning { color: #e6a23c; }
.status-danger { color: #f56c6c; }
.status-info { color: #909399; }
