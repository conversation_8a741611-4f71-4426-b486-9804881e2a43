<template>
  <div class="bom-detail">
    <!-- BOM基本信息 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>基本信息</h3>
        <div class="section-actions">
          <el-button type="primary" @click="handleEdit">编辑</el-button>
          <el-button @click="handleExport">导出</el-button>
        </div>
      </div>
      <div class="info-grid">
        <div class="info-item">
          <label>BOM编码:</label>
          <span>{{ bomData.code }}</span>
        </div>
        <div class="info-item">
          <label>BOM名称:</label>
          <span>{{ bomData.name }}</span>
        </div>
        <div class="info-item">
          <label>版本:</label>
          <span>{{ bomData.version }}</span>
        </div>
        <div class="info-item">
          <label>状态:</label>
          <el-tag :type="getStatusType(bomData.status)">
            {{ getStatusLabel(bomData.status) }}
          </el-tag>
        </div>
        <div class="info-item">
          <label>总成本:</label>
          <span class="cost-value">¥{{ bomData.totalCost?.toFixed(2) }}</span>
        </div>
        <div class="info-item">
          <label>创建人:</label>
          <span>{{ bomData.creator }}</span>
        </div>
        <div class="info-item">
          <label>创建时间:</label>
          <span>{{ formatDate(bomData.createTime) }}</span>
        </div>
        <div class="info-item">
          <label>更新时间:</label>
          <span>{{ formatDate(bomData.updateTime) }}</span>
        </div>
      </div>
      <div class="info-item full-width" v-if="bomData.description">
        <label>描述:</label>
        <p>{{ bomData.description }}</p>
      </div>
    </div>

    <!-- 物料清单 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>物料清单</h3>
        <div class="section-actions">
          <el-button @click="handleAnalyzeCost">成本分析</el-button>
          <el-button @click="handleCheckStock">库存检查</el-button>
        </div>
      </div>
      
      <el-table :data="bomData.materials" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="materialCode" label="物料编码" width="150" />
        <el-table-column prop="materialName" label="物料名称" min-width="200" />
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="unitPrice" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.unitPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="小计" width="100">
          <template #default="{ row }">
            ¥{{ row.totalPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="position" label="位号" width="100" />
        <el-table-column label="替代件" width="120">
          <template #default="{ row }">
            <el-tag
              v-for="alt in row.alternatives"
              :key="alt"
              size="small"
              class="alternative-tag"
            >
              {{ alt }}
            </el-tag>
            <span v-if="!row.alternatives || row.alternatives.length === 0" class="no-data">
              无
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="150">
          <template #default="{ row }">
            <span v-if="row.notes">{{ row.notes }}</span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 成本汇总 -->
      <div class="cost-summary">
        <div class="summary-item">
          <label>物料总数:</label>
          <span>{{ bomData.materials?.length || 0 }} 种</span>
        </div>
        <div class="summary-item">
          <label>总成本:</label>
          <span class="cost-value">¥{{ bomData.totalCost?.toFixed(2) }}</span>
        </div>
      </div>
    </div>

    <!-- 版本历史 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>版本历史</h3>
        <el-button @click="loadVersionHistory">刷新</el-button>
      </div>
      
      <el-table :data="versionHistory" v-loading="versionLoading">
        <el-table-column prop="version" label="版本" width="100" />
        <el-table-column prop="description" label="变更说明" min-width="200" />
        <el-table-column prop="creator" label="创建人" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="160" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '当前版本' : '历史版本' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="compareVersion(row)">
              对比
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="restoreVersion(row)"
              v-if="row.status !== 'active'"
            >
              恢复
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useBomStore } from '@/stores/bom'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const props = defineProps({
  bomData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'close'])

const bomStore = useBomStore()
const versionHistory = ref([])
const versionLoading = ref(false)

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    review: 'warning',
    active: 'success',
    obsolete: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusItem = bomStatus.find(item => item.value === status)
  return statusItem?.label || status
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const handleEdit = () => {
  emit('edit')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleAnalyzeCost = () => {
  ElMessage.info('成本分析功能开发中...')
}

const handleCheckStock = () => {
  ElMessage.info('库存检查功能开发中...')
}

const loadVersionHistory = async () => {
  versionLoading.value = true
  try {
    const result = await bomStore.getBomVersions(props.bomData.id)
    if (result.success) {
      versionHistory.value = result.data
    }
  } catch (error) {
    ElMessage.error('加载版本历史失败')
  } finally {
    versionLoading.value = false
  }
}

const compareVersion = (version) => {
  ElMessage.info(`版本对比功能开发中... (${version.version})`)
}

const restoreVersion = (version) => {
  ElMessage.info(`版本恢复功能开发中... (${version.version})`)
}

onMounted(() => {
  loadVersionHistory()
})
</script>

<style lang="scss" scoped>
.bom-detail {
  .detail-section {
    margin-bottom: 32px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
      
      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
        flex-direction: column;
        align-items: flex-start;
        
        p {
          margin: 8px 0 0 0;
          color: #606266;
          line-height: 1.5;
        }
      }
      
      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: #303133;
      }
      
      .cost-value {
        font-weight: 600;
        color: #e6a23c;
      }
    }
    
    .cost-summary {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      display: flex;
      justify-content: flex-end;
      gap: 32px;
      
      .summary-item {
        display: flex;
        align-items: center;
        
        label {
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
        }
        
        span {
          color: #303133;
          
          &.cost-value {
            font-weight: 600;
            color: #e6a23c;
            font-size: 16px;
          }
        }
      }
    }
    
    .alternative-tag {
      margin-right: 4px;
      margin-bottom: 4px;
    }
    
    .no-data {
      color: #c0c4cc;
      font-style: italic;
    }
  }
}
</style>
