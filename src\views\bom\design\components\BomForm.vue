<template>
  <div class="bom-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="BOM编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入BOM编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入BOM名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="form.version" placeholder="请输入版本号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="status in bomStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入BOM描述"
          />
        </el-form-item>
      </div>

      <!-- 物料清单 -->
      <div class="form-section">
        <div class="section-title">
          物料清单
          <el-button type="primary" size="small" @click="showMaterialDialog">
            <el-icon><Plus /></el-icon>
            添加物料
          </el-button>
        </div>
        
        <el-table :data="form.materials" border>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="materialCode" label="物料编码" width="150" />
          <el-table-column prop="materialName" label="物料名称" min-width="200" />
          <el-table-column prop="quantity" label="数量" width="100">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.quantity"
                :min="0"
                :precision="2"
                size="small"
                @change="updateMaterialCost($index)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="unitPrice" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.unitPrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="100">
            <template #default="{ row }">
              ¥{{ row.totalPrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="position" label="位号" width="100">
            <template #default="{ row, $index }">
              <el-input
                v-model="row.position"
                size="small"
                placeholder="如U1"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <el-button
                type="text"
                size="small"
                @click="removeMaterial($index)"
                class="danger-text"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="material-summary">
          <span>总成本: ¥{{ totalCost.toFixed(2) }}</span>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>

    <!-- 选择物料对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      title="选择物料"
      width="1000px"
    >
      <material-selector
        @select="handleMaterialSelect"
        @cancel="materialDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import MaterialSelector from './MaterialSelector.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref()
const submitting = ref(false)
const materialDialogVisible = ref(false)

const form = reactive({
  code: '',
  name: '',
  version: '1.0',
  status: 'draft',
  description: '',
  materials: []
})

const rules = {
  code: [
    { required: true, message: '请输入BOM编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入BOM名称', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

const bomStatusOptions = bomStatus

const totalCost = computed(() => {
  return form.materials.reduce((sum, material) => {
    return sum + (material.totalPrice || 0)
  }, 0)
})

const updateMaterialCost = (index) => {
  const material = form.materials[index]
  material.totalPrice = (material.quantity || 0) * (material.unitPrice || 0)
}

const showMaterialDialog = () => {
  materialDialogVisible.value = true
}

const handleMaterialSelect = (materials) => {
  materials.forEach(material => {
    const existingIndex = form.materials.findIndex(m => m.materialId === material.id)
    if (existingIndex === -1) {
      form.materials.push({
        id: Date.now().toString() + Math.random(),
        materialId: material.id,
        materialCode: material.code,
        materialName: material.name,
        quantity: 1,
        unit: material.unit,
        unitPrice: material.price,
        totalPrice: material.price,
        position: '',
        alternatives: material.alternatives || [],
        required: true,
        notes: ''
      })
    } else {
      ElMessage.warning(`物料 ${material.name} 已存在`)
    }
  })
  materialDialogVisible.value = false
}

const removeMaterial = (index) => {
  form.materials.splice(index, 1)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (form.materials.length === 0) {
      ElMessage.warning('请至少添加一个物料')
      return
    }
    
    submitting.value = true
    
    const submitData = {
      ...form,
      totalCost: totalCost.value
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听props变化，初始化表单数据
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(form, {
      code: newData.code || '',
      name: newData.name || '',
      version: newData.version || '1.0',
      status: newData.status || 'draft',
      description: newData.description || '',
      materials: newData.materials ? [...newData.materials] : []
    })
  } else {
    // 重置表单
    Object.assign(form, {
      code: '',
      name: '',
      version: '1.0',
      status: 'draft',
      description: '',
      materials: []
    })
  }
}, { immediate: true })

onMounted(() => {
  // 如果是新建，生成默认编码
  if (!props.isEdit && !form.code) {
    form.code = `EBOM-${Date.now().toString().slice(-6)}`
  }
})
</script>

<style lang="scss" scoped>
.bom-form {
  .form-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .material-summary {
    margin-top: 16px;
    text-align: right;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
  
  .form-actions {
    margin-top: 24px;
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-left: 12px;
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
