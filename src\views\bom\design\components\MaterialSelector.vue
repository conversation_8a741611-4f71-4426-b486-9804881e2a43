<template>
  <div class="material-selector">
    <!-- 搜索栏 -->
    <div class="search-section">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="请输入物料名称或编码"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="分类">
          <el-select v-model="searchForm.category" placeholder="请选择分类" clearable>
            <el-option
              v-for="category in materialCategories"
              :key="category.id"
              :label="category.name"
              :value="category.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 物料列表 -->
    <div class="material-list">
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="filteredMaterials"
        @selection-change="handleSelectionChange"
        max-height="400"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="code" label="物料编码" width="150" />
        <el-table-column prop="name" label="物料名称" min-width="200" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="price" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.price?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="stockQty" label="库存" width="100" />
        <el-table-column prop="supplier" label="供应商" width="120" />
      </el-table>
    </div>

    <!-- 已选物料 -->
    <div class="selected-materials" v-if="selectedMaterials.length > 0">
      <h4>已选物料 ({{ selectedMaterials.length }})</h4>
      <div class="selected-list">
        <el-tag
          v-for="material in selectedMaterials"
          :key="material.id"
          closable
          @close="removeMaterial(material)"
        >
          {{ material.name }}
        </el-tag>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :disabled="selectedMaterials.length === 0"
        @click="handleConfirm"
      >
        确定选择 ({{ selectedMaterials.length }})
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { mockMaterials, materialCategories } from '@/mock/materials'

const emit = defineEmits(['select', 'cancel'])

const tableRef = ref()
const loading = ref(false)
const selectedMaterials = ref([])

const searchForm = reactive({
  keyword: '',
  category: ''
})

const materials = ref([])

const filteredMaterials = computed(() => {
  let result = materials.value
  
  if (searchForm.keyword) {
    const keyword = searchForm.keyword.toLowerCase()
    result = result.filter(material =>
      material.name.toLowerCase().includes(keyword) ||
      material.code.toLowerCase().includes(keyword)
    )
  }
  
  if (searchForm.category) {
    result = result.filter(material => material.category === searchForm.category)
  }
  
  return result
})

const loadMaterials = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    materials.value = [...mockMaterials]
  } catch (error) {
    console.error('加载物料失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  // 搜索逻辑已在computed中处理
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.category = ''
}

const handleSelectionChange = (selection) => {
  selectedMaterials.value = selection
}

const removeMaterial = (material) => {
  const index = selectedMaterials.value.findIndex(m => m.id === material.id)
  if (index !== -1) {
    selectedMaterials.value.splice(index, 1)
    // 同时取消表格中的选择
    tableRef.value.toggleRowSelection(material, false)
  }
}

const handleConfirm = () => {
  emit('select', selectedMaterials.value)
}

const handleCancel = () => {
  emit('cancel')
}

onMounted(() => {
  loadMaterials()
})
</script>

<style lang="scss" scoped>
.material-selector {
  .search-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 6px;
  }
  
  .material-list {
    margin-bottom: 20px;
  }
  
  .selected-materials {
    margin-bottom: 20px;
    padding: 16px;
    background: #f0f9ff;
    border-radius: 6px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #303133;
      font-size: 14px;
    }
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
  
  .actions {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-left: 12px;
    }
  }
}
</style>
