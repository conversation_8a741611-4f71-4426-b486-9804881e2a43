<template>
  <div class="process-bom-detail">
    <!-- BOM基本信息 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>基本信息</h3>
        <div class="section-actions">
          <el-button type="primary" @click="handleEdit">编辑</el-button>
          <el-button @click="handleExport">导出</el-button>
        </div>
      </div>
      <div class="info-grid">
        <div class="info-item">
          <label>BOM编码:</label>
          <span>{{ bomData.code }}</span>
        </div>
        <div class="info-item">
          <label>BOM名称:</label>
          <span>{{ bomData.name }}</span>
        </div>
        <div class="info-item">
          <label>版本:</label>
          <span>{{ bomData.version }}</span>
        </div>
        <div class="info-item">
          <label>状态:</label>
          <el-tag :type="getStatusType(bomData.status)">
            {{ getStatusLabel(bomData.status) }}
          </el-tag>
        </div>
        <div class="info-item">
          <label>工艺步骤:</label>
          <span>{{ bomData.processSteps?.length || 0 }} 步</span>
        </div>
        <div class="info-item">
          <label>总工时:</label>
          <span>{{ totalTime }} 分钟</span>
        </div>
        <div class="info-item">
          <label>创建人:</label>
          <span>{{ bomData.creator }}</span>
        </div>
        <div class="info-item">
          <label>创建时间:</label>
          <span>{{ formatDate(bomData.createTime) }}</span>
        </div>
      </div>
      <div class="info-item full-width" v-if="bomData.description">
        <label>描述:</label>
        <p>{{ bomData.description }}</p>
      </div>
    </div>

    <!-- 工艺流程 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>工艺流程</h3>
        <div class="section-actions">
          <el-button @click="handleOptimize">工艺优化</el-button>
        </div>
      </div>
      
      <div class="process-flow">
        <div
          v-for="(step, index) in bomData.processSteps"
          :key="step.stepId"
          class="process-step"
        >
          <div class="step-header">
            <div class="step-number">{{ step.stepOrder }}</div>
            <div class="step-info">
              <h4>{{ step.stepName }}</h4>
              <p>{{ step.description }}</p>
            </div>
            <div class="step-meta">
              <div class="meta-item">
                <label>工作站:</label>
                <span>{{ step.workstation }}</span>
              </div>
              <div class="meta-item">
                <label>标准工时:</label>
                <span>{{ step.standardTime }} 分钟</span>
              </div>
            </div>
          </div>
          
          <!-- 步骤物料 -->
          <div class="step-materials" v-if="step.materials && step.materials.length > 0">
            <h5>使用物料</h5>
            <el-table :data="step.materials" size="small">
              <el-table-column prop="materialCode" label="物料编码" width="120" />
              <el-table-column prop="materialName" label="物料名称" min-width="150" />
              <el-table-column prop="quantity" label="数量" width="80" />
              <el-table-column prop="position" label="位号" width="80" />
            </el-table>
          </div>
          
          <!-- 连接线 -->
          <div v-if="index < bomData.processSteps.length - 1" class="step-connector">
            <el-icon><ArrowDown /></el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 物料汇总 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>物料汇总</h3>
        <div class="section-actions">
          <el-button @click="handleCheckStock">库存检查</el-button>
        </div>
      </div>
      
      <el-table :data="materialSummary">
        <el-table-column prop="materialCode" label="物料编码" width="150" />
        <el-table-column prop="materialName" label="物料名称" min-width="200" />
        <el-table-column prop="totalQuantity" label="总用量" width="100" />
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="使用步骤" min-width="150">
          <template #default="{ row }">
            <el-tag
              v-for="step in row.usedInSteps"
              :key="step"
              size="small"
              class="step-tag"
            >
              步骤{{ step }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="lossRate" label="损耗率" width="100">
          <template #default="{ row }">
            {{ (row.lossRate * 100).toFixed(1) }}%
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const props = defineProps({
  bomData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'close'])

const totalTime = computed(() => {
  return props.bomData.processSteps?.reduce((total, step) => {
    return total + (step.standardTime || 0)
  }, 0) || 0
})

const materialSummary = computed(() => {
  const summary = new Map()
  
  props.bomData.processSteps?.forEach((step, stepIndex) => {
    step.materials?.forEach(material => {
      const key = material.materialId || material.materialCode
      if (summary.has(key)) {
        const existing = summary.get(key)
        existing.totalQuantity += material.quantity || 0
        existing.usedInSteps.push(stepIndex + 1)
      } else {
        summary.set(key, {
          materialCode: material.materialCode,
          materialName: material.materialName,
          totalQuantity: material.quantity || 0,
          unit: material.unit || '个',
          usedInSteps: [stepIndex + 1],
          lossRate: 0.05 // 默认5%损耗率
        })
      }
    })
  })
  
  return Array.from(summary.values())
})

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    review: 'warning',
    active: 'success',
    obsolete: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusItem = bomStatus.find(item => item.value === status)
  return statusItem?.label || status
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const handleEdit = () => {
  emit('edit')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleOptimize = () => {
  ElMessage.info('工艺优化功能开发中...')
}

const handleCheckStock = () => {
  ElMessage.info('库存检查功能开发中...')
}
</script>

<style lang="scss" scoped>
.process-bom-detail {
  .detail-section {
    margin-bottom: 32px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
      
      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
        flex-direction: column;
        align-items: flex-start;
        
        p {
          margin: 8px 0 0 0;
          color: #606266;
          line-height: 1.5;
        }
      }
      
      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: #303133;
      }
    }
  }
  
  .process-flow {
    .process-step {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 16px;
      background: #fafafa;
      
      .step-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        
        .step-number {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #409eff;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-right: 16px;
          flex-shrink: 0;
        }
        
        .step-info {
          flex: 1;
          margin-right: 16px;
          
          h4 {
            font-size: 16px;
            color: #303133;
            margin: 0 0 4px 0;
          }
          
          p {
            font-size: 14px;
            color: #606266;
            margin: 0;
          }
        }
        
        .step-meta {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .meta-item {
            display: flex;
            align-items: center;
            font-size: 14px;
            
            label {
              color: #909399;
              margin-right: 8px;
            }
            
            span {
              color: #303133;
              font-weight: 500;
            }
          }
        }
      }
      
      .step-materials {
        h5 {
          font-size: 14px;
          color: #606266;
          margin: 0 0 12px 0;
        }
      }
    }
    
    .step-connector {
      text-align: center;
      margin: 8px 0;
      color: #909399;
      font-size: 20px;
    }
  }
  
  .step-tag {
    margin-right: 4px;
    margin-bottom: 4px;
  }
}
</style>
