<template>
  <div class="process-bom-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="BOM编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入BOM编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入BOM名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="form.version" placeholder="请输入版本号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="status in bomStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入工艺BOM描述"
          />
        </el-form-item>
      </div>

      <!-- 工艺步骤 -->
      <div class="form-section">
        <div class="section-title">
          工艺步骤
          <el-button type="primary" size="small" @click="addProcessStep">
            <el-icon><Plus /></el-icon>
            添加工艺步骤
          </el-button>
        </div>
        
        <div class="process-steps">
          <div
            v-for="(step, index) in form.processSteps"
            :key="step.id"
            class="process-step"
          >
            <div class="step-header">
              <span class="step-number">步骤 {{ index + 1 }}</span>
              <el-button
                type="text"
                size="small"
                @click="removeProcessStep(index)"
                class="danger-text"
              >
                删除
              </el-button>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="步骤名称">
                  <el-input v-model="step.stepName" placeholder="如SMT贴片" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="工作站">
                  <el-input v-model="step.workstation" placeholder="如SMT产线" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="标准工时(分钟)">
                  <el-input-number
                    v-model="step.standardTime"
                    :min="0"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="步骤描述">
              <el-input
                v-model="step.description"
                type="textarea"
                :rows="2"
                placeholder="请输入工艺步骤描述"
              />
            </el-form-item>
            
            <!-- 步骤物料 -->
            <div class="step-materials">
              <div class="materials-header">
                <span>步骤物料</span>
                <el-button
                  type="text"
                  size="small"
                  @click="showMaterialDialog(index)"
                >
                  添加物料
                </el-button>
              </div>
              
              <el-table :data="step.materials" size="small" border>
                <el-table-column prop="materialCode" label="物料编码" width="120" />
                <el-table-column prop="materialName" label="物料名称" min-width="150" />
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="position" label="位号" width="80" />
                <el-table-column label="操作" width="60">
                  <template #default="{ $index: materialIndex }">
                    <el-button
                      type="text"
                      size="small"
                      @click="removeStepMaterial(index, materialIndex)"
                      class="danger-text"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>

    <!-- 选择物料对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      title="选择物料"
      width="1000px"
    >
      <material-selector
        @select="handleMaterialSelect"
        @cancel="materialDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import MaterialSelector from '../../design/components/MaterialSelector.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref()
const submitting = ref(false)
const materialDialogVisible = ref(false)
const currentStepIndex = ref(-1)

const form = reactive({
  code: '',
  name: '',
  version: '1.0',
  status: 'draft',
  description: '',
  processSteps: []
})

const rules = {
  code: [
    { required: true, message: '请输入BOM编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入BOM名称', trigger: 'blur' }
  ],
  version: [
    { required: true, message: '请输入版本号', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

const bomStatusOptions = bomStatus

const addProcessStep = () => {
  const newStep = {
    id: Date.now().toString(),
    stepName: '',
    stepOrder: form.processSteps.length + 1,
    description: '',
    workstation: '',
    standardTime: 0,
    materials: []
  }
  form.processSteps.push(newStep)
}

const removeProcessStep = (index) => {
  form.processSteps.splice(index, 1)
  // 重新排序
  form.processSteps.forEach((step, idx) => {
    step.stepOrder = idx + 1
  })
}

const showMaterialDialog = (stepIndex) => {
  currentStepIndex.value = stepIndex
  materialDialogVisible.value = true
}

const handleMaterialSelect = (materials) => {
  if (currentStepIndex.value >= 0) {
    const step = form.processSteps[currentStepIndex.value]
    materials.forEach(material => {
      const existingIndex = step.materials.findIndex(m => m.materialId === material.id)
      if (existingIndex === -1) {
        step.materials.push({
          materialId: material.id,
          materialCode: material.code,
          materialName: material.name,
          quantity: 1,
          position: ''
        })
      }
    })
  }
  materialDialogVisible.value = false
}

const removeStepMaterial = (stepIndex, materialIndex) => {
  form.processSteps[stepIndex].materials.splice(materialIndex, 1)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (form.processSteps.length === 0) {
      ElMessage.warning('请至少添加一个工艺步骤')
      return
    }
    
    submitting.value = true
    
    const submitData = {
      ...form,
      totalCost: calculateTotalCost()
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const calculateTotalCost = () => {
  // 简化的成本计算逻辑
  return form.processSteps.reduce((total, step) => {
    return total + (step.standardTime || 0) * 2 // 假设每分钟工时成本2元
  }, 0)
}

const handleCancel = () => {
  emit('cancel')
}

// 监听props变化，初始化表单数据
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(form, {
      code: newData.code || '',
      name: newData.name || '',
      version: newData.version || '1.0',
      status: newData.status || 'draft',
      description: newData.description || '',
      processSteps: newData.processSteps ? [...newData.processSteps] : []
    })
  } else {
    // 重置表单
    Object.assign(form, {
      code: '',
      name: '',
      version: '1.0',
      status: 'draft',
      description: '',
      processSteps: []
    })
  }
}, { immediate: true })

onMounted(() => {
  // 如果是新建，生成默认编码
  if (!props.isEdit && !form.code) {
    form.code = `MBOM-${Date.now().toString().slice(-6)}`
  }
})
</script>

<style lang="scss" scoped>
.process-bom-form {
  .form-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  
  .process-steps {
    .process-step {
      border: 1px solid #ebeef5;
      border-radius: 6px;
      padding: 16px;
      margin-bottom: 16px;
      
      .step-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        .step-number {
          font-weight: 600;
          color: #303133;
        }
      }
      
      .step-materials {
        margin-top: 16px;
        
        .materials-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          font-weight: 500;
          color: #606266;
        }
      }
    }
  }
  
  .form-actions {
    margin-top: 24px;
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-left: 12px;
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
