<template>
  <div class="production-bom-detail">
    <!-- BOM基本信息 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>基本信息</h3>
        <div class="section-actions">
          <el-button type="primary" @click="handleEdit">编辑</el-button>
          <el-button type="warning" @click="handleCheckStock">检查库存</el-button>
          <el-button @click="handleExport">导出</el-button>
        </div>
      </div>
      <div class="info-grid">
        <div class="info-item">
          <label>BOM编码:</label>
          <span>{{ bomData.code }}</span>
        </div>
        <div class="info-item">
          <label>BOM名称:</label>
          <span>{{ bomData.name }}</span>
        </div>
        <div class="info-item">
          <label>生产订单:</label>
          <span>{{ bomData.productionOrder }}</span>
        </div>
        <div class="info-item">
          <label>生产数量:</label>
          <span>{{ bomData.productionQty }}</span>
        </div>
        <div class="info-item">
          <label>版本:</label>
          <span>{{ bomData.version }}</span>
        </div>
        <div class="info-item">
          <label>状态:</label>
          <el-tag :type="getStatusType(bomData.status)">
            {{ getStatusLabel(bomData.status) }}
          </el-tag>
        </div>
        <div class="info-item">
          <label>总成本:</label>
          <span class="cost-value">¥{{ bomData.totalCost?.toFixed(2) }}</span>
        </div>
        <div class="info-item">
          <label>缺料状态:</label>
          <el-tag :type="getShortageType()">
            {{ getShortageLabel() }}
          </el-tag>
        </div>
      </div>
      <div class="info-item full-width" v-if="bomData.description">
        <label>描述:</label>
        <p>{{ bomData.description }}</p>
      </div>
    </div>

    <!-- 物料清单 -->
    <div class="detail-section">
      <div class="section-header">
        <h3>物料清单</h3>
        <div class="section-actions">
          <el-button type="success" @click="handleGeneratePurchase">生成采购计划</el-button>
        </div>
      </div>
      
      <el-table :data="bomData.materials" border>
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="materialCode" label="物料编码" width="150" />
        <el-table-column prop="materialName" label="物料名称" min-width="200" />
        <el-table-column prop="requiredQty" label="需求数量" width="100" />
        <el-table-column prop="availableQty" label="可用库存" width="100">
          <template #default="{ row }">
            <span :class="{ 'shortage-qty': row.availableQty < row.requiredQty }">
              {{ row.availableQty }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="shortageQty" label="缺料数量" width="100">
          <template #default="{ row }">
            <span v-if="row.shortageQty > 0" class="shortage-qty">
              {{ row.shortageQty }}
            </span>
            <span v-else class="sufficient-qty">0</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column prop="unitPrice" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.unitPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" label="小计" width="100">
          <template #default="{ row }">
            ¥{{ row.totalPrice?.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="leadTime" label="交期(天)" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getMaterialStatusType(row.status)">
              {{ getMaterialStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 汇总信息 -->
      <div class="material-summary">
        <div class="summary-grid">
          <div class="summary-item">
            <label>物料总数:</label>
            <span>{{ bomData.materials?.length || 0 }} 种</span>
          </div>
          <div class="summary-item">
            <label>缺料项目:</label>
            <span class="shortage-qty">{{ shortageCount }} 种</span>
          </div>
          <div class="summary-item">
            <label>总成本:</label>
            <span class="cost-value">¥{{ bomData.totalCost?.toFixed(2) }}</span>
          </div>
          <div class="summary-item">
            <label>缺料成本:</label>
            <span class="shortage-qty">¥{{ shortageCost.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 缺料分析 -->
    <div class="detail-section" v-if="shortageItems.length > 0">
      <div class="section-header">
        <h3>缺料分析</h3>
        <div class="section-actions">
          <el-button type="danger" @click="handleUrgentPurchase">紧急采购</el-button>
        </div>
      </div>
      
      <el-table :data="shortageItems" border>
        <el-table-column prop="materialCode" label="物料编码" width="150" />
        <el-table-column prop="materialName" label="物料名称" min-width="200" />
        <el-table-column prop="shortageQty" label="缺料数量" width="100">
          <template #default="{ row }">
            <span class="shortage-qty">{{ row.shortageQty }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit" label="单位" width="80" />
        <el-table-column label="缺料金额" width="120">
          <template #default="{ row }">
            <span class="shortage-qty">¥{{ (row.shortageQty * row.unitPrice).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="supplier" label="供应商" width="120" />
        <el-table-column prop="leadTime" label="交期(天)" width="100" />
        <el-table-column label="建议操作" width="120">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handlePurchaseItem(row)">
              立即采购
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const props = defineProps({
  bomData: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['edit', 'close'])

const shortageCount = computed(() => {
  return props.bomData.materials?.filter(material => material.status === 'shortage').length || 0
})

const shortageItems = computed(() => {
  return props.bomData.materials?.filter(material => material.status === 'shortage') || []
})

const shortageCost = computed(() => {
  return shortageItems.value.reduce((total, item) => {
    return total + (item.shortageQty * item.unitPrice)
  }, 0)
})

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    review: 'warning',
    active: 'success',
    obsolete: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusItem = bomStatus.find(item => item.value === status)
  return statusItem?.label || status
}

const getShortageType = () => {
  if (shortageCount.value > 0) return 'danger'
  return 'success'
}

const getShortageLabel = () => {
  if (shortageCount.value > 0) return `缺料 ${shortageCount.value} 种`
  return '库存充足'
}

const getMaterialStatusType = (status) => {
  const statusMap = {
    sufficient: 'success',
    shortage: 'danger',
    ordered: 'warning'
  }
  return statusMap[status] || 'info'
}

const getMaterialStatusLabel = (status) => {
  const statusMap = {
    sufficient: '充足',
    shortage: '缺料',
    ordered: '已订购'
  }
  return statusMap[status] || status
}

const handleEdit = () => {
  emit('edit')
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleCheckStock = () => {
  ElMessage.info('库存检查功能开发中...')
}

const handleGeneratePurchase = () => {
  ElMessage.info('生成采购计划功能开发中...')
}

const handleUrgentPurchase = () => {
  ElMessage.info('紧急采购功能开发中...')
}

const handlePurchaseItem = (item) => {
  ElMessage.info(`立即采购 ${item.materialName} 功能开发中...`)
}
</script>

<style lang="scss" scoped>
.production-bom-detail {
  .detail-section {
    margin-bottom: 32px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      
      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin: 0;
      }
      
      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      margin-bottom: 16px;
    }
    
    .info-item {
      display: flex;
      align-items: center;
      
      &.full-width {
        grid-column: 1 / -1;
        flex-direction: column;
        align-items: flex-start;
        
        p {
          margin: 8px 0 0 0;
          color: #606266;
          line-height: 1.5;
        }
      }
      
      label {
        font-weight: 500;
        color: #606266;
        margin-right: 8px;
        min-width: 80px;
      }
      
      span {
        color: #303133;
      }
      
      .cost-value {
        font-weight: 600;
        color: #e6a23c;
      }
    }
    
    .material-summary {
      margin-top: 16px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }
      
      .summary-item {
        display: flex;
        align-items: center;
        
        label {
          font-weight: 500;
          color: #606266;
          margin-right: 8px;
        }
        
        span {
          color: #303133;
          font-weight: 600;
          
          &.cost-value {
            color: #e6a23c;
          }
        }
      }
    }
  }
  
  .shortage-qty {
    color: #f56c6c;
    font-weight: 600;
  }
  
  .sufficient-qty {
    color: #67c23a;
    font-weight: 600;
  }
}
</style>
