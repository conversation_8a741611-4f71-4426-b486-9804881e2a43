<template>
  <div class="production-bom-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
    >
      <!-- 基本信息 -->
      <div class="form-section">
        <div class="section-title">基本信息</div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="BOM编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入BOM编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="BOM名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入BOM名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="生产订单号" prop="productionOrder">
              <el-input v-model="form.productionOrder" placeholder="请输入生产订单号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生产数量" prop="productionQty">
              <el-input-number
                v-model="form.productionQty"
                :min="1"
                style="width: 100%"
                placeholder="请输入生产数量"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="版本号" prop="version">
              <el-input v-model="form.version" placeholder="请输入版本号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option
                  v-for="status in bomStatusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入生产BOM描述"
          />
        </el-form-item>
      </div>

      <!-- 物料清单 -->
      <div class="form-section">
        <div class="section-title">
          物料清单
          <div class="section-actions">
            <el-button type="primary" size="small" @click="showMaterialDialog">
              <el-icon><Plus /></el-icon>
              添加物料
            </el-button>
            <el-button type="warning" size="small" @click="checkAllStock">
              <el-icon><Warning /></el-icon>
              检查库存
            </el-button>
          </div>
        </div>
        
        <el-table :data="form.materials" border>
          <el-table-column type="index" label="序号" width="60" />
          <el-table-column prop="materialCode" label="物料编码" width="150" />
          <el-table-column prop="materialName" label="物料名称" min-width="180" />
          <el-table-column prop="requiredQty" label="需求数量" width="100">
            <template #default="{ row, $index }">
              <el-input-number
                v-model="row.requiredQty"
                :min="0"
                :precision="2"
                size="small"
                @change="updateMaterialCost($index)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="availableQty" label="可用库存" width="100">
            <template #default="{ row }">
              <span :class="{ 'shortage-qty': row.availableQty < row.requiredQty }">
                {{ row.availableQty }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="shortageQty" label="缺料数量" width="100">
            <template #default="{ row }">
              <span v-if="row.shortageQty > 0" class="shortage-qty">
                {{ row.shortageQty }}
              </span>
              <span v-else class="sufficient-qty">0</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="unitPrice" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.unitPrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalPrice" label="小计" width="100">
            <template #default="{ row }">
              ¥{{ row.totalPrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getMaterialStatusType(row.status)">
                {{ getMaterialStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <el-button
                type="text"
                size="small"
                @click="removeMaterial($index)"
                class="danger-text"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="material-summary">
          <div class="summary-item">
            <span>总成本: ¥{{ totalCost.toFixed(2) }}</span>
          </div>
          <div class="summary-item">
            <span class="shortage-qty">缺料项目: {{ shortageCount }}</span>
          </div>
        </div>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSubmit" :loading="submitting">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>

    <!-- 选择物料对话框 -->
    <el-dialog
      v-model="materialDialogVisible"
      title="选择物料"
      width="1000px"
    >
      <material-selector
        @select="handleMaterialSelect"
        @cancel="materialDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage } from 'element-plus'
import MaterialSelector from '../../design/components/MaterialSelector.vue'

const props = defineProps({
  formData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['submit', 'cancel'])

const formRef = ref()
const submitting = ref(false)
const materialDialogVisible = ref(false)

const form = reactive({
  code: '',
  name: '',
  productionOrder: '',
  productionQty: 1,
  version: '1.0',
  status: 'draft',
  description: '',
  materials: []
})

const rules = {
  code: [
    { required: true, message: '请输入BOM编码', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入BOM名称', trigger: 'blur' }
  ],
  productionOrder: [
    { required: true, message: '请输入生产订单号', trigger: 'blur' }
  ],
  productionQty: [
    { required: true, message: '请输入生产数量', trigger: 'blur' }
  ]
}

const bomStatusOptions = bomStatus

const totalCost = computed(() => {
  return form.materials.reduce((sum, material) => {
    return sum + (material.totalPrice || 0)
  }, 0)
})

const shortageCount = computed(() => {
  return form.materials.filter(material => material.status === 'shortage').length
})

const getMaterialStatusType = (status) => {
  const statusMap = {
    sufficient: 'success',
    shortage: 'danger',
    ordered: 'warning'
  }
  return statusMap[status] || 'info'
}

const getMaterialStatusLabel = (status) => {
  const statusMap = {
    sufficient: '充足',
    shortage: '缺料',
    ordered: '已订购'
  }
  return statusMap[status] || status
}

const updateMaterialCost = (index) => {
  const material = form.materials[index]
  const requiredQty = material.requiredQty || 0
  const availableQty = material.availableQty || 0
  
  material.totalPrice = requiredQty * (material.unitPrice || 0)
  material.shortageQty = Math.max(0, requiredQty - availableQty)
  material.status = material.shortageQty > 0 ? 'shortage' : 'sufficient'
}

const showMaterialDialog = () => {
  materialDialogVisible.value = true
}

const handleMaterialSelect = (materials) => {
  materials.forEach(material => {
    const existingIndex = form.materials.findIndex(m => m.materialId === material.id)
    if (existingIndex === -1) {
      const requiredQty = form.productionQty || 1
      const availableQty = material.stockQty || 0
      const shortageQty = Math.max(0, requiredQty - availableQty)
      
      form.materials.push({
        id: Date.now().toString() + Math.random(),
        materialId: material.id,
        materialCode: material.code,
        materialName: material.name,
        requiredQty: requiredQty,
        availableQty: availableQty,
        shortageQty: shortageQty,
        unit: material.unit,
        unitPrice: material.price,
        totalPrice: requiredQty * material.price,
        supplier: material.supplier,
        leadTime: material.leadTime,
        status: shortageQty > 0 ? 'shortage' : 'sufficient'
      })
    } else {
      ElMessage.warning(`物料 ${material.name} 已存在`)
    }
  })
  materialDialogVisible.value = false
}

const removeMaterial = (index) => {
  form.materials.splice(index, 1)
}

const checkAllStock = () => {
  form.materials.forEach((material, index) => {
    updateMaterialCost(index)
  })
  ElMessage.success('库存检查完成')
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (form.materials.length === 0) {
      ElMessage.warning('请至少添加一个物料')
      return
    }
    
    submitting.value = true
    
    const submitData = {
      ...form,
      totalCost: totalCost.value
    }
    
    emit('submit', submitData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听生产数量变化，自动更新物料需求
watch(() => form.productionQty, (newQty) => {
  if (newQty && newQty > 0) {
    form.materials.forEach((material, index) => {
      material.requiredQty = newQty
      updateMaterialCost(index)
    })
  }
})

// 监听props变化，初始化表单数据
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(form, {
      code: newData.code || '',
      name: newData.name || '',
      productionOrder: newData.productionOrder || '',
      productionQty: newData.productionQty || 1,
      version: newData.version || '1.0',
      status: newData.status || 'draft',
      description: newData.description || '',
      materials: newData.materials ? [...newData.materials] : []
    })
  } else {
    // 重置表单
    Object.assign(form, {
      code: '',
      name: '',
      productionOrder: '',
      productionQty: 1,
      version: '1.0',
      status: 'draft',
      description: '',
      materials: []
    })
  }
}, { immediate: true })

onMounted(() => {
  // 如果是新建，生成默认编码
  if (!props.isEdit && !form.code) {
    form.code = `PBOM-${Date.now().toString().slice(-6)}`
    form.productionOrder = `PO-${Date.now().toString().slice(-8)}`
  }
})
</script>

<style lang="scss" scoped>
.production-bom-form {
  .form-section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #ebeef5;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .section-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .material-summary {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    
    .summary-item {
      font-size: 16px;
      font-weight: 600;
    }
  }
  
  .shortage-qty {
    color: #f56c6c;
    font-weight: 600;
  }
  
  .sufficient-qty {
    color: #67c23a;
    font-weight: 600;
  }
  
  .form-actions {
    margin-top: 24px;
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
    
    .el-button {
      margin-left: 12px;
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
