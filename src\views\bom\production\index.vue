<template>
  <div class="bom-production">
    <!-- 页面标题和操作栏 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">生产用料清单</h2>
            <p class="page-desc">管理生产执行阶段的物料清单，支持库存检查和采购建议</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建生产BOM
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar content-card">
      <div class="card-body">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入BOM名称或生产订单号"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="status in bomStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="缺料状态">
            <el-select v-model="searchForm.shortageStatus" placeholder="请选择缺料状态" clearable>
              <el-option label="充足" value="sufficient" />
              <el-option label="缺料" value="shortage" />
              <el-option label="已订购" value="ordered" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- BOM列表 -->
    <div class="bom-list content-card">
      <div class="card-header">
        <div class="card-title">生产BOM列表</div>
        <div class="table-actions">
          <el-button type="warning" @click="handleBatchCheck">
            <el-icon><Warning /></el-icon>
            批量库存检查
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="bomList"
          stripe
          @row-click="handleRowClick"
        >
          <el-table-column prop="code" label="BOM编码" width="150" />
          <el-table-column prop="name" label="BOM名称" min-width="180" />
          <el-table-column prop="productionOrder" label="生产订单" width="140" />
          <el-table-column prop="productionQty" label="生产数量" width="100" />
          <el-table-column prop="version" label="版本" width="80" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="缺料状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getShortageType(row)">
                {{ getShortageLabel(row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalCost" label="总成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click.stop="handleView(row)">查看</el-button>
              <el-button type="text" @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click.stop="handleCheckStock(row)">库存检查</el-button>
              <el-button type="text" @click.stop="handleDelete(row)" class="danger-text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑生产BOM对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1000px"
      @close="handleDialogClose"
    >
      <production-bom-form
        ref="bomFormRef"
        :form-data="currentBom"
        :is-edit="isEdit"
        @submit="handleSubmit"
        @cancel="handleDialogClose"
      />
    </el-dialog>

    <!-- 生产BOM详情对话框 -->
    <el-dialog
      v-model="detailVisible"
      title="生产BOM详情"
      width="1400px"
    >
      <production-bom-detail
        v-if="detailVisible"
        :bom-data="selectedBom"
        @edit="handleEditFromDetail"
        @close="detailVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useBomStore } from '@/stores/bom'
import { bomStatus } from '@/mock/bom'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import ProductionBomForm from './components/ProductionBomForm.vue'
import ProductionBomDetail from './components/ProductionBomDetail.vue'

const bomStore = useBomStore()

const loading = ref(false)
const dialogVisible = ref(false)
const detailVisible = ref(false)
const isEdit = ref(false)
const currentBom = ref(null)
const selectedBom = ref(null)
const bomFormRef = ref()

const searchForm = reactive({
  keyword: '',
  status: '',
  shortageStatus: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const bomList = ref([])

const bomStatusOptions = bomStatus

const dialogTitle = computed(() => isEdit.value ? '编辑生产BOM' : '新建生产BOM')

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    review: 'warning',
    active: 'success',
    obsolete: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusItem = bomStatus.find(item => item.value === status)
  return statusItem?.label || status
}

const getShortageType = (row) => {
  // 简化的缺料状态判断
  const hasShortage = row.materials?.some(m => m.status === 'shortage')
  if (hasShortage) return 'danger'
  const hasOrdered = row.materials?.some(m => m.status === 'ordered')
  if (hasOrdered) return 'warning'
  return 'success'
}

const getShortageLabel = (row) => {
  const hasShortage = row.materials?.some(m => m.status === 'shortage')
  if (hasShortage) return '缺料'
  const hasOrdered = row.materials?.some(m => m.status === 'ordered')
  if (hasOrdered) return '已订购'
  return '充足'
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const loadBomList = async () => {
  loading.value = true
  try {
    const params = {
      type: 'production',
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    }
    const result = await bomStore.getBomList(params)
    if (result.success) {
      bomList.value = result.data
      pagination.total = result.data.length
    }
  } catch (error) {
    ElMessage.error('加载生产BOM列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadBomList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.shortageStatus = ''
  handleSearch()
}

const handleSizeChange = () => {
  loadBomList()
}

const handlePageChange = () => {
  loadBomList()
}

const showCreateDialog = () => {
  isEdit.value = false
  currentBom.value = null
  dialogVisible.value = true
}

const handleRowClick = (row) => {
  handleView(row)
}

const handleView = async (row) => {
  const result = await bomStore.getBomDetail(row.id)
  if (result.success) {
    selectedBom.value = result.data
    detailVisible.value = true
  }
}

const handleEdit = (row) => {
  isEdit.value = true
  currentBom.value = { ...row }
  dialogVisible.value = true
}

const handleEditFromDetail = () => {
  detailVisible.value = false
  handleEdit(selectedBom.value)
}

const handleCheckStock = (row) => {
  ElMessage.info(`库存检查功能开发中... (${row.name})`)
}

const handleBatchCheck = () => {
  ElMessage.info('批量库存检查功能开发中...')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个生产BOM吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const result = await bomStore.deleteBom(row.id)
    if (result.success) {
      ElMessage.success('删除成功')
      loadBomList()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async (formData) => {
  try {
    let result
    if (isEdit.value) {
      result = await bomStore.updateBom(currentBom.value.id, formData)
    } else {
      result = await bomStore.createBom({ ...formData, type: 'production' })
    }
    
    if (result.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      loadBomList()
    }
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  currentBom.value = null
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  loadBomList()
})
</script>

<style lang="scss" scoped>
.bom-production {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .search-bar {
    margin-bottom: 20px;
  }
  
  .bom-list {
    .table-actions {
      display: flex;
      gap: 8px;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
