<template>
  <div class="bom-sales">
    <!-- 页面标题和操作栏 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">销售配置清单</h2>
            <p class="page-desc">管理客户定制配置，支持个性化产品配置和报价</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建销售配置
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar content-card">
      <div class="card-body">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入配置名称或客户名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="status in bomStatusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 销售配置列表 -->
    <div class="bom-list content-card">
      <div class="card-header">
        <div class="card-title">销售配置列表</div>
        <div class="table-actions">
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="bomList"
          stripe
          @row-click="handleRowClick"
        >
          <el-table-column prop="code" label="配置编码" width="150" />
          <el-table-column prop="name" label="配置名称" min-width="200" />
          <el-table-column prop="customerName" label="客户名称" width="150" />
          <el-table-column prop="version" label="版本" width="100" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="totalCost" label="配置成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="salePrice" label="销售价格" width="120">
            <template #default="{ row }">
              ¥{{ row.salePrice?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="creator" label="创建人" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="240" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click.stop="handleView(row)">查看</el-button>
              <el-button type="text" @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click.stop="handleQuote(row)">生成报价</el-button>
              <el-button type="text" @click.stop="handleDelete(row)" class="danger-text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="1000px"
      @close="handleDialogClose"
    >
      <div class="simple-form">
        <el-form :model="currentBom" label-width="120px">
          <el-form-item label="配置编码">
            <el-input v-model="currentBom.code" placeholder="请输入配置编码" />
          </el-form-item>
          <el-form-item label="配置名称">
            <el-input v-model="currentBom.name" placeholder="请输入配置名称" />
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input v-model="currentBom.customerName" placeholder="请输入客户名称" />
          </el-form-item>
          <el-form-item label="销售价格">
            <el-input-number v-model="currentBom.salePrice" :min="0" :precision="2" style="width: 100%" />
          </el-form-item>
          <el-form-item label="描述">
            <el-input v-model="currentBom.description" type="textarea" :rows="3" />
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useBomStore } from '@/stores/bom'
import { bomStatus } from '@/mock/bom'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const bomStore = useBomStore()

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentBom = ref({})

const searchForm = reactive({
  keyword: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const bomList = ref([])
const bomStatusOptions = bomStatus

const dialogTitle = computed(() => isEdit.value ? '编辑销售配置' : '新建销售配置')

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    review: 'warning',
    active: 'success',
    obsolete: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusItem = bomStatus.find(item => item.value === status)
  return statusItem?.label || status
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const loadBomList = async () => {
  loading.value = true
  try {
    // 模拟销售配置数据
    bomList.value = [
      {
        id: 'SBOM001',
        code: 'SBOM-TH-CUST001',
        name: '智能温湿度监测器-客户定制版',
        customerName: '某科技公司',
        version: '1.0',
        status: 'active',
        totalCost: 156.85,
        salePrice: 299.00,
        creator: '刘销售',
        createTime: '2024-03-01T10:00:00Z',
        description: '客户要求增加蓝牙功能'
      }
    ]
    pagination.total = bomList.value.length
  } catch (error) {
    ElMessage.error('加载销售配置列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadBomList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  handleSearch()
}

const handleSizeChange = () => {
  loadBomList()
}

const handlePageChange = () => {
  loadBomList()
}

const showCreateDialog = () => {
  isEdit.value = false
  currentBom.value = {
    code: `SBOM-${Date.now().toString().slice(-6)}`,
    name: '',
    customerName: '',
    salePrice: 0,
    description: '',
    status: 'draft'
  }
  dialogVisible.value = true
}

const handleRowClick = (row) => {
  ElMessage.info(`查看销售配置详情功能开发中... (${row.name})`)
}

const handleView = (row) => {
  ElMessage.info(`查看销售配置详情功能开发中... (${row.name})`)
}

const handleEdit = (row) => {
  isEdit.value = true
  currentBom.value = { ...row }
  dialogVisible.value = true
}

const handleQuote = (row) => {
  ElMessage.info(`生成报价单功能开发中... (${row.name})`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个销售配置吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadBomList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = () => {
  ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
  dialogVisible.value = false
  loadBomList()
}

const handleDialogClose = () => {
  dialogVisible.value = false
  currentBom.value = {}
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  loadBomList()
})
</script>

<style lang="scss" scoped>
.bom-sales {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .search-bar {
    margin-bottom: 20px;
  }
  
  .bom-list {
    .table-actions {
      display: flex;
      gap: 8px;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  
  .simple-form {
    .form-actions {
      margin-top: 24px;
      text-align: right;
      padding-top: 16px;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        margin-left: 12px;
      }
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
