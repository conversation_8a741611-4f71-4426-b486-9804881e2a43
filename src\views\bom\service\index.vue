<template>
  <div class="bom-service">
    <!-- 页面标题和操作栏 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">售后用料清单</h2>
            <p class="page-desc">管理售后服务用料，支持快速维修定位和替换件管理</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建服务BOM
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-bar content-card">
      <div class="card-body">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入产品SN或客户名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="服务类型">
            <el-select v-model="searchForm.serviceType" placeholder="请选择服务类型" clearable>
              <el-option label="维修" value="repair" />
              <el-option label="更换" value="replace" />
              <el-option label="升级" value="upgrade" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 服务BOM列表 -->
    <div class="bom-list content-card">
      <div class="card-header">
        <div class="card-title">服务BOM列表</div>
        <div class="table-actions">
          <el-button type="warning" @click="handleQuickSearch">
            <el-icon><Search /></el-icon>
            快速查找
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="bomList"
          stripe
          @row-click="handleRowClick"
        >
          <el-table-column prop="code" label="服务编码" width="150" />
          <el-table-column prop="productSN" label="产品SN" width="150" />
          <el-table-column prop="productName" label="产品名称" min-width="180" />
          <el-table-column prop="customerName" label="客户名称" width="120" />
          <el-table-column prop="serviceType" label="服务类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getServiceTypeColor(row.serviceType)">
                {{ getServiceTypeLabel(row.serviceType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="serviceCost" label="服务成本" width="120">
            <template #default="{ row }">
              ¥{{ row.serviceCost?.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="serviceEngineer" label="服务工程师" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click.stop="handleView(row)">查看</el-button>
              <el-button type="text" @click.stop="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click.stop="handleComplete(row)">完成服务</el-button>
              <el-button type="text" @click.stop="handleDelete(row)" class="danger-text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="800px"
      @close="handleDialogClose"
    >
      <div class="simple-form">
        <el-form :model="currentBom" label-width="120px">
          <el-form-item label="服务编码">
            <el-input v-model="currentBom.code" placeholder="请输入服务编码" />
          </el-form-item>
          <el-form-item label="产品SN">
            <el-input v-model="currentBom.productSN" placeholder="请输入产品序列号" />
          </el-form-item>
          <el-form-item label="产品名称">
            <el-input v-model="currentBom.productName" placeholder="请输入产品名称" />
          </el-form-item>
          <el-form-item label="客户名称">
            <el-input v-model="currentBom.customerName" placeholder="请输入客户名称" />
          </el-form-item>
          <el-form-item label="服务类型">
            <el-select v-model="currentBom.serviceType" placeholder="请选择服务类型">
              <el-option label="维修" value="repair" />
              <el-option label="更换" value="replace" />
              <el-option label="升级" value="upgrade" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务工程师">
            <el-input v-model="currentBom.serviceEngineer" placeholder="请输入服务工程师" />
          </el-form-item>
          <el-form-item label="问题描述">
            <el-input v-model="currentBom.description" type="textarea" :rows="3" />
          </el-form-item>
        </el-form>
        <div class="form-actions">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 快速查找对话框 -->
    <el-dialog
      v-model="quickSearchVisible"
      title="快速查找产品"
      width="600px"
    >
      <div class="quick-search">
        <el-form inline>
          <el-form-item label="产品SN">
            <el-input v-model="quickSearchSN" placeholder="请输入产品序列号" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuickSearchSubmit">查找</el-button>
          </el-form-item>
        </el-form>
        <div v-if="quickSearchResult" class="search-result">
          <h4>查找结果</h4>
          <div class="result-info">
            <p><strong>产品名称:</strong> {{ quickSearchResult.productName }}</p>
            <p><strong>客户名称:</strong> {{ quickSearchResult.customerName }}</p>
            <p><strong>出厂日期:</strong> {{ quickSearchResult.manufactureDate }}</p>
            <p><strong>保修状态:</strong> {{ quickSearchResult.warrantyStatus }}</p>
          </div>
          <div class="result-actions">
            <el-button type="primary" @click="createServiceFromSearch">创建服务单</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { bomStatus } from '@/mock/bom'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const loading = ref(false)
const dialogVisible = ref(false)
const quickSearchVisible = ref(false)
const isEdit = ref(false)
const currentBom = ref({})
const quickSearchSN = ref('')
const quickSearchResult = ref(null)

const searchForm = reactive({
  keyword: '',
  serviceType: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const bomList = ref([])

const dialogTitle = computed(() => isEdit.value ? '编辑服务BOM' : '新建服务BOM')

const getStatusType = (status) => {
  const statusMap = {
    draft: 'info',
    active: 'success',
    completed: 'success',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const statusMap = {
    draft: '草稿',
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getServiceTypeColor = (type) => {
  const colorMap = {
    repair: 'warning',
    replace: 'danger',
    upgrade: 'success'
  }
  return colorMap[type] || 'info'
}

const getServiceTypeLabel = (type) => {
  const labelMap = {
    repair: '维修',
    replace: '更换',
    upgrade: '升级'
  }
  return labelMap[type] || type
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const loadBomList = async () => {
  loading.value = true
  try {
    // 模拟服务BOM数据
    bomList.value = [
      {
        id: 'ServiceBOM001',
        code: 'SVC-20240301-001',
        productSN: 'TH001-20240115-001',
        productName: '智能温湿度监测器',
        customerName: '某科技公司',
        serviceType: 'repair',
        status: 'active',
        serviceCost: 85.50,
        serviceEngineer: '陈技师',
        createTime: '2024-03-01T14:30:00Z',
        description: '显示屏不亮，需要更换显示模块'
      }
    ]
    pagination.total = bomList.value.length
  } catch (error) {
    ElMessage.error('加载服务BOM列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadBomList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.serviceType = ''
  handleSearch()
}

const handleSizeChange = () => {
  loadBomList()
}

const handlePageChange = () => {
  loadBomList()
}

const showCreateDialog = () => {
  isEdit.value = false
  currentBom.value = {
    code: `SVC-${dayjs().format('YYYYMMDD')}-${Math.random().toString().slice(-3)}`,
    productSN: '',
    productName: '',
    customerName: '',
    serviceType: 'repair',
    serviceEngineer: '',
    description: '',
    status: 'draft'
  }
  dialogVisible.value = true
}

const handleRowClick = (row) => {
  ElMessage.info(`查看服务BOM详情功能开发中... (${row.code})`)
}

const handleView = (row) => {
  ElMessage.info(`查看服务BOM详情功能开发中... (${row.code})`)
}

const handleEdit = (row) => {
  isEdit.value = true
  currentBom.value = { ...row }
  dialogVisible.value = true
}

const handleComplete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要完成这个服务吗？', '确认完成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success'
    })
    ElMessage.success('服务已完成')
    loadBomList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这个服务BOM吗？', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadBomList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = () => {
  ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
  dialogVisible.value = false
  loadBomList()
}

const handleDialogClose = () => {
  dialogVisible.value = false
  currentBom.value = {}
}

const handleQuickSearch = () => {
  quickSearchVisible.value = true
  quickSearchSN.value = ''
  quickSearchResult.value = null
}

const handleQuickSearchSubmit = () => {
  if (!quickSearchSN.value) {
    ElMessage.warning('请输入产品序列号')
    return
  }
  
  // 模拟查找结果
  quickSearchResult.value = {
    productSN: quickSearchSN.value,
    productName: '智能温湿度监测器',
    customerName: '某科技公司',
    manufactureDate: '2024-01-15',
    warrantyStatus: '在保修期内'
  }
}

const createServiceFromSearch = () => {
  quickSearchVisible.value = false
  currentBom.value = {
    code: `SVC-${dayjs().format('YYYYMMDD')}-${Math.random().toString().slice(-3)}`,
    productSN: quickSearchResult.value.productSN,
    productName: quickSearchResult.value.productName,
    customerName: quickSearchResult.value.customerName,
    serviceType: 'repair',
    serviceEngineer: '',
    description: '',
    status: 'draft'
  }
  isEdit.value = false
  dialogVisible.value = true
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

onMounted(() => {
  loadBomList()
})
</script>

<style lang="scss" scoped>
.bom-service {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .search-bar {
    margin-bottom: 20px;
  }
  
  .bom-list {
    .table-actions {
      display: flex;
      gap: 8px;
    }
    
    .pagination-wrapper {
      margin-top: 20px;
      display: flex;
      justify-content: center;
    }
  }
  
  .simple-form {
    .form-actions {
      margin-top: 24px;
      text-align: right;
      padding-top: 16px;
      border-top: 1px solid #ebeef5;
      
      .el-button {
        margin-left: 12px;
      }
    }
  }
  
  .quick-search {
    .search-result {
      margin-top: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      
      h4 {
        margin: 0 0 12px 0;
        color: #303133;
      }
      
      .result-info {
        margin-bottom: 16px;
        
        p {
          margin: 8px 0;
          color: #606266;
        }
      }
      
      .result-actions {
        text-align: right;
      }
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
