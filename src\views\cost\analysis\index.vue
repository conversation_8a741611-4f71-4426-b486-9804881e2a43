<template>
  <div class="cost-analysis">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">成本分析</h2>
            <p class="page-desc">实时成本计算和分析，支持多维度成本对比</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 成本概览 -->
    <div class="cost-overview">
      <div class="overview-card content-card">
        <div class="card-header">
          <div class="card-title">成本概览</div>
        </div>
        <div class="card-body">
          <div class="overview-grid">
            <div class="overview-item">
              <div class="item-icon material-cost">
                <el-icon><Box /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.materialCost.toLocaleString() }}</div>
                <div class="item-label">物料成本</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="item-icon labor-cost">
                <el-icon><User /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.laborCost.toLocaleString() }}</div>
                <div class="item-label">人工成本</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="item-icon overhead-cost">
                <el-icon><Setting /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.overheadCost.toLocaleString() }}</div>
                <div class="item-label">制造费用</div>
              </div>
            </div>
            <div class="overview-item">
              <div class="item-icon total-cost">
                <el-icon><Money /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.totalCost.toLocaleString() }}</div>
                <div class="item-label">总成本</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 产品成本分析 -->
    <div class="product-cost-analysis">
      <div class="analysis-card content-card">
        <div class="card-header">
          <div class="card-title">产品成本分析</div>
          <div class="card-actions">
            <el-select v-model="selectedProduct" placeholder="选择产品" @change="loadProductCost">
              <el-option
                v-for="product in products"
                :key="product.id"
                :label="product.name"
                :value="product.id"
              />
            </el-select>
          </div>
        </div>
        <div class="card-body">
          <div class="cost-breakdown">
            <div class="breakdown-chart">
              <div ref="costChartRef" style="width: 100%; height: 300px;"></div>
            </div>
            <div class="breakdown-details">
              <div class="detail-item" v-for="item in costBreakdown" :key="item.name">
                <div class="detail-label">{{ item.name }}</div>
                <div class="detail-value">¥{{ item.value.toFixed(2) }}</div>
                <div class="detail-percent">{{ item.percent }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 成本趋势分析 -->
    <div class="cost-trend">
      <div class="trend-card content-card">
        <div class="card-header">
          <div class="card-title">成本趋势分析</div>
          <div class="card-actions">
            <el-radio-group v-model="trendPeriod" @change="loadTrendData">
              <el-radio-button label="week">最近一周</el-radio-button>
              <el-radio-button label="month">最近一月</el-radio-button>
              <el-radio-button label="quarter">最近一季</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="card-body">
          <div ref="trendChartRef" style="width: 100%; height: 400px;"></div>
        </div>
      </div>
    </div>

    <!-- 成本对比分析 -->
    <div class="cost-comparison">
      <div class="comparison-card content-card">
        <div class="card-header">
          <div class="card-title">产品成本对比</div>
          <div class="card-actions">
            <el-button @click="handleExportComparison">导出对比</el-button>
          </div>
        </div>
        <div class="card-body">
          <el-table :data="comparisonData" border>
            <el-table-column prop="productName" label="产品名称" min-width="150" />
            <el-table-column prop="materialCost" label="物料成本" width="120">
              <template #default="{ row }">
                ¥{{ row.materialCost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="laborCost" label="人工成本" width="120">
              <template #default="{ row }">
                ¥{{ row.laborCost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="overheadCost" label="制造费用" width="120">
              <template #default="{ row }">
                ¥{{ row.overheadCost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="totalCost" label="总成本" width="120">
              <template #default="{ row }">
                ¥{{ row.totalCost.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column prop="salePrice" label="销售价格" width="120">
              <template #default="{ row }">
                ¥{{ row.salePrice.toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="毛利率" width="100">
              <template #default="{ row }">
                <span :class="getProfitClass(row.profitRate)">
                  {{ row.profitRate.toFixed(1) }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button type="text" @click="handleAnalyzeProduct(row)">详细分析</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

const costChartRef = ref()
const trendChartRef = ref()
const selectedProduct = ref('')
const trendPeriod = ref('month')

const overview = reactive({
  materialCost: 285600,
  laborCost: 125400,
  overheadCost: 89200,
  totalCost: 500200
})

const products = ref([
  { id: '1', name: '智能温湿度监测器' },
  { id: '2', name: '多功能开发板' },
  { id: '3', name: '无线传感器节点' }
])

const costBreakdown = ref([
  { name: '主控芯片', value: 45.80, percent: 29.2 },
  { name: '传感器', value: 21.70, percent: 13.8 },
  { name: '显示模块', value: 15.60, percent: 9.9 },
  { name: '电源模块', value: 12.50, percent: 8.0 },
  { name: '外壳结构', value: 25.00, percent: 15.9 },
  { name: '其他物料', value: 36.25, percent: 23.2 }
])

const comparisonData = ref([
  {
    productName: '智能温湿度监测器',
    materialCost: 156.85,
    laborCost: 45.00,
    overheadCost: 28.50,
    totalCost: 230.35,
    salePrice: 299.00,
    profitRate: 22.9
  },
  {
    productName: '多功能开发板',
    materialCost: 285.60,
    laborCost: 68.00,
    overheadCost: 42.80,
    totalCost: 396.40,
    salePrice: 499.00,
    profitRate: 20.6
  },
  {
    productName: '无线传感器节点',
    materialCost: 89.50,
    laborCost: 25.00,
    overheadCost: 18.20,
    totalCost: 132.70,
    salePrice: 199.00,
    profitRate: 33.3
  }
])

const getProfitClass = (rate) => {
  if (rate >= 30) return 'high-profit'
  if (rate >= 20) return 'medium-profit'
  return 'low-profit'
}

const loadProductCost = () => {
  // 模拟加载产品成本数据
  ElMessage.success('产品成本数据已更新')
}

const loadTrendData = () => {
  // 模拟加载趋势数据
  initTrendChart()
}

const initCostChart = () => {
  if (!costChartRef.value) return
  
  const chart = echarts.init(costChartRef.value)
  const option = {
    title: {
      text: '成本构成',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    series: [
      {
        name: '成本构成',
        type: 'pie',
        radius: ['40%', '70%'],
        data: costBreakdown.value.map(item => ({
          name: item.name,
          value: item.value
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    title: {
      text: '成本趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['物料成本', '人工成本', '制造费用'],
      top: 30
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '成本(元)'
    },
    series: [
      {
        name: '物料成本',
        type: 'line',
        data: [150, 155, 148, 162, 158, 156]
      },
      {
        name: '人工成本',
        type: 'line',
        data: [42, 45, 44, 46, 45, 45]
      },
      {
        name: '制造费用',
        type: 'line',
        data: [25, 28, 26, 29, 28, 28]
      }
    ]
  }
  chart.setOption(option)
}

const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

const handleExportComparison = () => {
  ElMessage.info('导出成本对比功能开发中...')
}

const handleAnalyzeProduct = (row) => {
  ElMessage.info(`详细分析 ${row.productName} 功能开发中...`)
}

onMounted(() => {
  nextTick(() => {
    initCostChart()
    initTrendChart()
  })
})
</script>

<style lang="scss" scoped>
.cost-analysis {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .cost-overview {
    margin-bottom: 20px;
    
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      
      .overview-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;
        
        .item-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          
          &.material-cost {
            background: #e1f3ff;
            color: #409eff;
          }
          
          &.labor-cost {
            background: #f0f9ff;
            color: #67c23a;
          }
          
          &.overhead-cost {
            background: #fef0e6;
            color: #e6a23c;
          }
          
          &.total-cost {
            background: #fef0f0;
            color: #f56c6c;
          }
        }
        
        .item-content {
          .item-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }
          
          .item-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .product-cost-analysis,
  .cost-trend,
  .cost-comparison {
    margin-bottom: 20px;
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
    
    .cost-breakdown {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: 20px;
      
      .breakdown-details {
        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          .detail-label {
            color: #606266;
          }
          
          .detail-value {
            font-weight: 600;
            color: #303133;
          }
          
          .detail-percent {
            color: #909399;
            font-size: 12px;
          }
        }
      }
    }
  }
  
  .high-profit {
    color: #67c23a;
    font-weight: 600;
  }
  
  .medium-profit {
    color: #e6a23c;
    font-weight: 600;
  }
  
  .low-profit {
    color: #f56c6c;
    font-weight: 600;
  }
}

@media (max-width: 768px) {
  .cost-analysis {
    .overview-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .cost-breakdown {
      grid-template-columns: 1fr;
    }
  }
}
</style>
