<template>
  <div class="cost-report">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">成本报表</h2>
            <p class="page-desc">生成各类成本报表，支持多种格式导出</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleGenerateReport">
              <el-icon><Document /></el-icon>
              生成报表
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 报表筛选条件 -->
    <div class="report-filter content-card">
      <div class="card-header">
        <div class="card-title">筛选条件</div>
      </div>
      <div class="card-body">
        <el-form :model="filterForm" inline>
          <el-form-item label="报表类型">
            <el-select v-model="filterForm.reportType" placeholder="请选择报表类型">
              <el-option label="产品成本报表" value="product" />
              <el-option label="物料成本报表" value="material" />
              <el-option label="工艺成本报表" value="process" />
              <el-option label="订单成本报表" value="order" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item label="产品">
            <el-select v-model="filterForm.productId" placeholder="请选择产品" clearable>
              <el-option
                v-for="product in products"
                :key="product.id"
                :label="product.name"
                :value="product.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 报表数据 -->
    <div class="report-data content-card">
      <div class="card-header">
        <div class="card-title">{{ getReportTitle() }}</div>
        <div class="card-actions">
          <el-button @click="handleExportExcel">
            <el-icon><Download /></el-icon>
            导出Excel
          </el-button>
          <el-button @click="handleExportPDF">
            <el-icon><Document /></el-icon>
            导出PDF
          </el-button>
          <el-button @click="handlePrint">
            <el-icon><Printer /></el-icon>
            打印
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <!-- 产品成本报表 -->
        <el-table
          v-if="filterForm.reportType === 'product'"
          :data="productReportData"
          border
          v-loading="loading"
        >
          <el-table-column prop="productCode" label="产品编码" width="150" />
          <el-table-column prop="productName" label="产品名称" min-width="200" />
          <el-table-column prop="bomVersion" label="BOM版本" width="100" />
          <el-table-column prop="materialCost" label="物料成本" width="120">
            <template #default="{ row }">
              ¥{{ row.materialCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="laborCost" label="人工成本" width="120">
            <template #default="{ row }">
              ¥{{ row.laborCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="overheadCost" label="制造费用" width="120">
            <template #default="{ row }">
              ¥{{ row.overheadCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalCost" label="总成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160">
            <template #default="{ row }">
              {{ formatDate(row.updateTime) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 物料成本报表 -->
        <el-table
          v-if="filterForm.reportType === 'material'"
          :data="materialReportData"
          border
          v-loading="loading"
        >
          <el-table-column prop="materialCode" label="物料编码" width="150" />
          <el-table-column prop="materialName" label="物料名称" min-width="200" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="currentPrice" label="当前价格" width="120">
            <template #default="{ row }">
              ¥{{ row.currentPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="avgPrice" label="平均价格" width="120">
            <template #default="{ row }">
              ¥{{ row.avgPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="usageQty" label="用量" width="100" />
          <el-table-column prop="totalCost" label="总成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="supplier" label="供应商" width="120" />
        </el-table>

        <!-- 工艺成本报表 -->
        <el-table
          v-if="filterForm.reportType === 'process'"
          :data="processReportData"
          border
          v-loading="loading"
        >
          <el-table-column prop="processCode" label="工艺编码" width="150" />
          <el-table-column prop="processName" label="工艺名称" min-width="200" />
          <el-table-column prop="workstation" label="工作站" width="120" />
          <el-table-column prop="standardTime" label="标准工时(分钟)" width="140" />
          <el-table-column prop="hourlyRate" label="小时费率" width="120">
            <template #default="{ row }">
              ¥{{ row.hourlyRate.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="laborCost" label="人工成本" width="120">
            <template #default="{ row }">
              ¥{{ row.laborCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="overheadRate" label="制造费用率" width="120">
            <template #default="{ row }">
              {{ row.overheadRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="totalCost" label="总成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost.toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>

        <!-- 订单成本报表 -->
        <el-table
          v-if="filterForm.reportType === 'order'"
          :data="orderReportData"
          border
          v-loading="loading"
        >
          <el-table-column prop="orderNo" label="订单号" width="150" />
          <el-table-column prop="productName" label="产品名称" min-width="180" />
          <el-table-column prop="quantity" label="数量" width="100" />
          <el-table-column prop="unitCost" label="单位成本" width="120">
            <template #default="{ row }">
              ¥{{ row.unitCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalCost" label="总成本" width="120">
            <template #default="{ row }">
              ¥{{ row.totalCost.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="salePrice" label="销售价格" width="120">
            <template #default="{ row }">
              ¥{{ row.salePrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="profit" label="利润" width="120">
            <template #default="{ row }">
              <span :class="getProfitClass(row.profit)">
                ¥{{ row.profit.toFixed(2) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="profitRate" label="利润率" width="100">
            <template #default="{ row }">
              <span :class="getProfitClass(row.profit)">
                {{ row.profitRate.toFixed(1) }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="orderDate" label="订单日期" width="120" />
        </el-table>

        <!-- 汇总信息 -->
        <div class="report-summary" v-if="reportSummary">
          <div class="summary-grid">
            <div class="summary-item">
              <label>记录总数:</label>
              <span>{{ reportSummary.totalRecords }}</span>
            </div>
            <div class="summary-item">
              <label>总成本:</label>
              <span class="cost-value">¥{{ reportSummary.totalCost.toFixed(2) }}</span>
            </div>
            <div class="summary-item" v-if="reportSummary.totalProfit !== undefined">
              <label>总利润:</label>
              <span :class="getProfitClass(reportSummary.totalProfit)">
                ¥{{ reportSummary.totalProfit.toFixed(2) }}
              </span>
            </div>
            <div class="summary-item" v-if="reportSummary.avgProfitRate !== undefined">
              <label>平均利润率:</label>
              <span :class="getProfitClass(reportSummary.totalProfit)">
                {{ reportSummary.avgProfitRate.toFixed(1) }}%
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const loading = ref(false)

const filterForm = reactive({
  reportType: 'product',
  dateRange: [],
  productId: ''
})

const products = ref([
  { id: '1', name: '智能温湿度监测器' },
  { id: '2', name: '多功能开发板' },
  { id: '3', name: '无线传感器节点' }
])

// 模拟报表数据
const productReportData = ref([
  {
    productCode: 'TH-001',
    productName: '智能温湿度监测器',
    bomVersion: '1.2',
    materialCost: 156.85,
    laborCost: 45.00,
    overheadCost: 28.50,
    totalCost: 230.35,
    updateTime: '2024-03-01T10:00:00Z'
  }
])

const materialReportData = ref([
  {
    materialCode: 'ESP32-WROOM-32',
    materialName: 'ESP32无线模块',
    category: '无线通信',
    unit: '个',
    currentPrice: 18.50,
    avgPrice: 18.20,
    usageQty: 100,
    totalCost: 1850.00,
    supplier: '乐鑫科技'
  }
])

const processReportData = ref([
  {
    processCode: 'SMT-001',
    processName: 'SMT贴片',
    workstation: 'SMT产线',
    standardTime: 15,
    hourlyRate: 120.00,
    laborCost: 30.00,
    overheadRate: 50,
    totalCost: 45.00
  }
])

const orderReportData = ref([
  {
    orderNo: 'PO-20240301',
    productName: '智能温湿度监测器',
    quantity: 100,
    unitCost: 230.35,
    totalCost: 23035.00,
    salePrice: 29900.00,
    profit: 6865.00,
    profitRate: 22.9,
    orderDate: '2024-03-01'
  }
])

const reportSummary = computed(() => {
  switch (filterForm.reportType) {
    case 'product':
      return {
        totalRecords: productReportData.value.length,
        totalCost: productReportData.value.reduce((sum, item) => sum + item.totalCost, 0)
      }
    case 'material':
      return {
        totalRecords: materialReportData.value.length,
        totalCost: materialReportData.value.reduce((sum, item) => sum + item.totalCost, 0)
      }
    case 'process':
      return {
        totalRecords: processReportData.value.length,
        totalCost: processReportData.value.reduce((sum, item) => sum + item.totalCost, 0)
      }
    case 'order':
      const totalProfit = orderReportData.value.reduce((sum, item) => sum + item.profit, 0)
      const totalSales = orderReportData.value.reduce((sum, item) => sum + item.salePrice * item.quantity, 0)
      return {
        totalRecords: orderReportData.value.length,
        totalCost: orderReportData.value.reduce((sum, item) => sum + item.totalCost, 0),
        totalProfit,
        avgProfitRate: totalSales > 0 ? (totalProfit / totalSales) * 100 : 0
      }
    default:
      return null
  }
})

const getReportTitle = () => {
  const titleMap = {
    product: '产品成本报表',
    material: '物料成本报表',
    process: '工艺成本报表',
    order: '订单成本报表'
  }
  return titleMap[filterForm.reportType] || '成本报表'
}

const getProfitClass = (profit) => {
  if (profit > 0) return 'positive-profit'
  if (profit < 0) return 'negative-profit'
  return ''
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const handleFilter = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    ElMessage.success('报表数据已更新')
  }, 1000)
}

const handleReset = () => {
  filterForm.reportType = 'product'
  filterForm.dateRange = []
  filterForm.productId = ''
  handleFilter()
}

const handleGenerateReport = () => {
  ElMessage.success('报表生成成功')
}

const handleExportExcel = () => {
  ElMessage.info('导出Excel功能开发中...')
}

const handleExportPDF = () => {
  ElMessage.info('导出PDF功能开发中...')
}

const handlePrint = () => {
  ElMessage.info('打印功能开发中...')
}

onMounted(() => {
  // 设置默认日期范围为最近一个月
  const endDate = dayjs().format('YYYY-MM-DD')
  const startDate = dayjs().subtract(1, 'month').format('YYYY-MM-DD')
  filterForm.dateRange = [startDate, endDate]
})
</script>

<style lang="scss" scoped>
.cost-report {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .report-filter {
    margin-bottom: 20px;
  }
  
  .report-data {
    .card-actions {
      display: flex;
      gap: 8px;
    }
    
    .report-summary {
      margin-top: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 6px;
      
      .summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        
        .summary-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          label {
            font-weight: 500;
            color: #606266;
          }
          
          span {
            font-weight: 600;
            color: #303133;
            
            &.cost-value {
              color: #e6a23c;
            }
          }
        }
      }
    }
  }
  
  .positive-profit {
    color: #67c23a;
    font-weight: 600;
  }
  
  .negative-profit {
    color: #f56c6c;
    font-weight: 600;
  }
}
</style>
