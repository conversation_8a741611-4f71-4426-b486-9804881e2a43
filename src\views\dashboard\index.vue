<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-card content-card">
      <div class="card-body">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ userStore.userInfo?.name }}！</h2>
            <p>今天是 {{ currentDate }}，{{ getGreeting() }}</p>
          </div>
          <div class="welcome-avatar">
            <el-avatar :src="userStore.userInfo?.avatar" :size="60" />
          </div>
        </div>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card content-card">
        <div class="card-body">
          <div class="stat-content">
            <div class="stat-icon bom-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalBoms }}</div>
              <div class="stat-label">BOM总数</div>
            </div>
          </div>
        </div>
      </div>

      <div class="stat-card content-card">
        <div class="card-body">
          <div class="stat-content">
            <div class="stat-icon material-icon">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalMaterials }}</div>
              <div class="stat-label">物料种类</div>
            </div>
          </div>
        </div>
      </div>

      <div class="stat-card content-card">
        <div class="card-body">
          <div class="stat-content">
            <div class="stat-icon cost-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">¥{{ stats.totalCost.toLocaleString() }}</div>
              <div class="stat-label">总成本</div>
            </div>
          </div>
        </div>
      </div>

      <div class="stat-card content-card">
        <div class="card-body">
          <div class="stat-content">
            <div class="stat-icon shortage-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.shortageItems }}</div>
              <div class="stat-label">缺料项目</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="quick-actions content-card">
      <div class="card-header">
        <div class="card-title">快速操作</div>
      </div>
      <div class="card-body">
        <div class="action-grid">
          <div
            v-for="action in quickActions"
            :key="action.name"
            class="action-item"
            @click="handleQuickAction(action)"
          >
            <div class="action-icon" :class="action.iconClass">
              <el-icon><component :is="action.icon" /></el-icon>
            </div>
            <div class="action-text">
              <div class="action-name">{{ action.name }}</div>
              <div class="action-desc">{{ action.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities content-card">
      <div class="card-header">
        <div class="card-title">最近活动</div>
        <el-button type="text" @click="viewAllActivities">查看全部</el-button>
      </div>
      <div class="card-body">
        <div class="activity-list">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="activity-item"
          >
            <div class="activity-icon" :class="activity.typeClass">
              <el-icon><component :is="activity.icon" /></el-icon>
            </div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title }}</div>
              <div class="activity-desc">{{ activity.description }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

const currentDate = computed(() => dayjs().format('YYYY年MM月DD日'))

const stats = ref({
  totalBoms: 24,
  totalMaterials: 156,
  totalCost: 285600,
  shortageItems: 3
})

const quickActions = [
  {
    name: '创建设计BOM',
    description: '新建产品设计用料清单',
    icon: 'Edit',
    iconClass: 'design-action',
    route: '/bom/design',
    permission: 'bom:design:create'
  },
  {
    name: '生产用料清单',
    description: '查看生产执行用料',
    icon: 'Operation',
    iconClass: 'production-action',
    route: '/bom/production',
    permission: 'bom:production:view'
  },
  {
    name: '成本分析',
    description: '查看产品成本分析',
    icon: 'TrendCharts',
    iconClass: 'cost-action',
    route: '/cost/analysis',
    permission: 'cost:analysis:view'
  },
  {
    name: '采购计划',
    description: '管理物料采购计划',
    icon: 'ShoppingBag',
    iconClass: 'purchase-action',
    route: '/purchase/plan',
    permission: 'purchase:plan:view'
  }
]

const recentActivities = ref([
  {
    id: '1',
    title: '创建了新的设计BOM',
    description: '智能温湿度监测器 v1.2',
    time: '2小时前',
    icon: 'Document',
    typeClass: 'create-activity'
  },
  {
    id: '2',
    title: '更新了物料价格',
    description: 'ESP32-WROOM-32 价格调整',
    time: '4小时前',
    icon: 'Money',
    typeClass: 'update-activity'
  },
  {
    id: '3',
    title: '审批了工艺BOM',
    description: '多功能开发板工艺流程',
    time: '1天前',
    icon: 'Check',
    typeClass: 'approve-activity'
  },
  {
    id: '4',
    title: '生成了采购计划',
    description: '本月物料采购需求',
    time: '2天前',
    icon: 'ShoppingCart',
    typeClass: 'generate-activity'
  }
])

const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const handleQuickAction = (action) => {
  if (action.permission && !userStore.hasPermission(action.permission)) {
    ElMessage.warning('您没有权限执行此操作')
    return
  }
  
  if (action.route) {
    router.push(action.route)
  } else {
    ElMessage.info(`${action.name}功能开发中...`)
  }
}

const viewAllActivities = () => {
  ElMessage.info('活动日志功能开发中...')
}

onMounted(() => {
  // 这里可以加载实际的统计数据
})
</script>

<style lang="scss" scoped>
.dashboard {
  .welcome-card {
    margin-bottom: 24px;
    
    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .welcome-text {
        h2 {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        p {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 24px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
          
          &.bom-icon {
            background: #e1f3ff;
            color: #409eff;
          }
          
          &.material-icon {
            background: #f0f9ff;
            color: #67c23a;
          }
          
          &.cost-icon {
            background: #fef0e6;
            color: #e6a23c;
          }
          
          &.shortage-icon {
            background: #fef0f0;
            color: #f56c6c;
          }
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .quick-actions {
    margin-bottom: 24px;
    
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      
      .action-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          border-color: #409eff;
          background-color: #f0f9ff;
        }
        
        .action-icon {
          width: 40px;
          height: 40px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 20px;
          
          &.design-action {
            background: #e1f3ff;
            color: #409eff;
          }
          
          &.production-action {
            background: #f0f9ff;
            color: #67c23a;
          }
          
          &.cost-action {
            background: #fef0e6;
            color: #e6a23c;
          }
          
          &.purchase-action {
            background: #f4f4f5;
            color: #909399;
          }
        }
        
        .action-text {
          .action-name {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .action-desc {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .recent-activities {
    .activity-list {
      .activity-item {
        display: flex;
        align-items: flex-start;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .activity-icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          font-size: 16px;
          flex-shrink: 0;
          
          &.create-activity {
            background: #e1f3ff;
            color: #409eff;
          }
          
          &.update-activity {
            background: #fef0e6;
            color: #e6a23c;
          }
          
          &.approve-activity {
            background: #f0f9ff;
            color: #67c23a;
          }
          
          &.generate-activity {
            background: #f4f4f5;
            color: #909399;
          }
        }
        
        .activity-content {
          flex: 1;
          
          .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .activity-desc {
            font-size: 13px;
            color: #606266;
            margin-bottom: 4px;
          }
          
          .activity-time {
            font-size: 12px;
            color: #909399;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .dashboard {
    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .action-grid {
      grid-template-columns: 1fr;
    }
    
    .welcome-content {
      flex-direction: column;
      text-align: center;
      
      .welcome-text {
        margin-bottom: 16px;
      }
    }
  }
}
</style>
