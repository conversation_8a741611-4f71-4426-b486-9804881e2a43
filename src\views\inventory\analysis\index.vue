<template>
  <div class="inventory-analysis">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">库存分析</h2>
            <p class="page-desc">实时库存监控和分析，优化库存结构</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存概览 -->
    <div class="inventory-overview">
      <div class="overview-card content-card">
        <div class="card-header">
          <div class="card-title">库存概览</div>
        </div>
        <div class="card-body">
          <div class="overview-grid">
            <div class="overview-item total-value">
              <div class="item-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.totalValue.toLocaleString() }}</div>
                <div class="item-label">库存总价值</div>
              </div>
            </div>
            <div class="overview-item total-items">
              <div class="item-icon">
                <el-icon><Box /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.totalItems }}</div>
                <div class="item-label">物料种类</div>
              </div>
            </div>
            <div class="overview-item low-stock">
              <div class="item-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.lowStockItems }}</div>
                <div class="item-label">低库存预警</div>
              </div>
            </div>
            <div class="overview-item turnover">
              <div class="item-icon">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.turnoverRate }}</div>
                <div class="item-label">库存周转率</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 库存分析图表 -->
    <div class="inventory-charts">
      <div class="chart-row">
        <div class="chart-card content-card">
          <div class="card-header">
            <div class="card-title">库存分布</div>
          </div>
          <div class="card-body">
            <div ref="distributionChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>
        <div class="chart-card content-card">
          <div class="card-header">
            <div class="card-title">库存趋势</div>
          </div>
          <div class="card-body">
            <div ref="trendChartRef" style="width: 100%; height: 300px;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-bar content-card">
      <div class="card-body">
        <el-form :model="filterForm" inline>
          <el-form-item label="库存状态">
            <el-select v-model="filterForm.stockStatus" placeholder="请选择状态" clearable>
              <el-option label="正常" value="normal" />
              <el-option label="低库存" value="low" />
              <el-option label="超储" value="excess" />
              <el-option label="零库存" value="zero" />
            </el-select>
          </el-form-item>
          <el-form-item label="物料分类">
            <el-select v-model="filterForm.category" placeholder="请选择分类" clearable>
              <el-option label="主控芯片" value="主控芯片" />
              <el-option label="传感器" value="传感器" />
              <el-option label="显示器件" value="显示器件" />
              <el-option label="被动器件" value="被动器件" />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商">
            <el-select v-model="filterForm.supplier" placeholder="请选择供应商" clearable>
              <el-option label="意法半导体" value="意法半导体" />
              <el-option label="乐鑫科技" value="乐鑫科技" />
              <el-option label="奥松电子" value="奥松电子" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 库存明细 -->
    <div class="inventory-detail content-card">
      <div class="card-header">
        <div class="card-title">库存明细</div>
        <div class="card-actions">
          <el-button type="warning" @click="handleStockAlert">
            <el-icon><Bell /></el-icon>
            库存预警
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="inventoryList"
          @row-click="handleRowClick"
        >
          <el-table-column prop="materialCode" label="物料编码" width="150" />
          <el-table-column prop="materialName" label="物料名称" min-width="200" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="currentStock" label="当前库存" width="100">
            <template #default="{ row }">
              <span :class="getStockClass(row)">{{ row.currentStock }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="safetyStock" label="安全库存" width="100" />
          <el-table-column prop="maxStock" label="最大库存" width="100" />
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="unitPrice" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.unitPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalValue" label="库存价值" width="120">
            <template #default="{ row }">
              ¥{{ row.totalValue.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="supplier" label="供应商" width="120" />
          <el-table-column prop="lastInDate" label="最后入库" width="120" />
          <el-table-column label="库存状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row)">
                {{ getStatusLabel(row) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="handleAdjust(row)">库存调整</el-button>
              <el-button type="text" @click="handleHistory(row)">出入库记录</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 库存调整对话框 -->
    <el-dialog
      v-model="adjustDialogVisible"
      title="库存调整"
      width="500px"
    >
      <el-form :model="adjustForm" label-width="120px">
        <el-form-item label="物料名称">
          <el-input v-model="adjustForm.materialName" disabled />
        </el-form-item>
        <el-form-item label="当前库存">
          <el-input v-model="adjustForm.currentStock" disabled />
        </el-form-item>
        <el-form-item label="调整类型">
          <el-radio-group v-model="adjustForm.adjustType">
            <el-radio label="in">入库</el-radio>
            <el-radio label="out">出库</el-radio>
            <el-radio label="adjust">盘点调整</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="调整数量">
          <el-input-number
            v-model="adjustForm.adjustQty"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因">
          <el-input v-model="adjustForm.reason" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="adjustDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAdjust">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

const loading = ref(false)
const adjustDialogVisible = ref(false)
const distributionChartRef = ref()
const trendChartRef = ref()

const overview = reactive({
  totalValue: 1256800,
  totalItems: 156,
  lowStockItems: 8,
  turnoverRate: '3.2次/年'
})

const filterForm = reactive({
  stockStatus: '',
  category: '',
  supplier: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const adjustForm = reactive({
  materialName: '',
  currentStock: 0,
  adjustType: 'in',
  adjustQty: 0,
  reason: ''
})

const inventoryList = ref([
  {
    id: '1',
    materialCode: 'ESP32-WROOM-32',
    materialName: 'ESP32无线模块',
    category: '无线通信',
    currentStock: 200,
    safetyStock: 50,
    maxStock: 500,
    unit: '个',
    unitPrice: 18.50,
    totalValue: 3700.00,
    supplier: '乐鑫科技',
    lastInDate: '2024-02-28'
  },
  {
    id: '2',
    materialCode: 'DHT22',
    materialName: 'DHT22温湿度传感器',
    category: '传感器',
    currentStock: 30,
    safetyStock: 30,
    maxStock: 200,
    unit: '个',
    unitPrice: 12.80,
    totalValue: 384.00,
    supplier: '奥松电子',
    lastInDate: '2024-02-25'
  }
])

const getStockClass = (row) => {
  if (row.currentStock <= 0) return 'zero-stock'
  if (row.currentStock <= row.safetyStock) return 'low-stock'
  if (row.currentStock >= row.maxStock) return 'excess-stock'
  return 'normal-stock'
}

const getStatusType = (row) => {
  if (row.currentStock <= 0) return 'danger'
  if (row.currentStock <= row.safetyStock) return 'warning'
  if (row.currentStock >= row.maxStock) return 'info'
  return 'success'
}

const getStatusLabel = (row) => {
  if (row.currentStock <= 0) return '零库存'
  if (row.currentStock <= row.safetyStock) return '低库存'
  if (row.currentStock >= row.maxStock) return '超储'
  return '正常'
}

const initDistributionChart = () => {
  if (!distributionChartRef.value) return
  
  const chart = echarts.init(distributionChartRef.value)
  const option = {
    title: {
      text: '库存分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [
      {
        name: '库存分布',
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { name: '主控芯片', value: 35 },
          { name: '传感器', value: 28 },
          { name: '显示器件', value: 18 },
          { name: '被动器件', value: 12 },
          { name: '其他', value: 7 }
        ]
      }
    ]
  }
  chart.setOption(option)
}

const initTrendChart = () => {
  if (!trendChartRef.value) return
  
  const chart = echarts.init(trendChartRef.value)
  const option = {
    title: {
      text: '库存趋势',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value',
      name: '库存价值(万元)'
    },
    series: [
      {
        name: '库存价值',
        type: 'line',
        data: [120, 125, 118, 132, 128, 126],
        smooth: true
      }
    ]
  }
  chart.setOption(option)
}

const handleFilter = () => {
  ElMessage.success('筛选条件已应用')
}

const handleReset = () => {
  filterForm.stockStatus = ''
  filterForm.category = ''
  filterForm.supplier = ''
  handleFilter()
}

const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

const handleStockAlert = () => {
  ElMessage.info('库存预警功能开发中...')
}

const handleExport = () => {
  ElMessage.info('导出库存数据功能开发中...')
}

const handleRowClick = (row) => {
  ElMessage.info(`查看 ${row.materialName} 详细信息功能开发中...`)
}

const handleAdjust = (row) => {
  adjustForm.materialName = row.materialName
  adjustForm.currentStock = row.currentStock
  adjustForm.adjustType = 'in'
  adjustForm.adjustQty = 0
  adjustForm.reason = ''
  adjustDialogVisible.value = true
}

const handleSaveAdjust = () => {
  ElMessage.success('库存调整成功')
  adjustDialogVisible.value = false
}

const handleHistory = (row) => {
  ElMessage.info(`查看 ${row.materialName} 出入库记录功能开发中...`)
}

const handleSizeChange = () => {
  // 处理分页大小变化
}

const handlePageChange = () => {
  // 处理页码变化
}

onMounted(() => {
  pagination.total = inventoryList.value.length
  nextTick(() => {
    initDistributionChart()
    initTrendChart()
  })
})
</script>

<style lang="scss" scoped>
.inventory-analysis {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .inventory-overview {
    margin-bottom: 20px;
    
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      
      .overview-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;
        
        .item-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
        }
        
        &.total-value .item-icon {
          background: #fef0e6;
          color: #e6a23c;
        }
        
        &.total-items .item-icon {
          background: #f0f9ff;
          color: #409eff;
        }
        
        &.low-stock .item-icon {
          background: #fef0f0;
          color: #f56c6c;
        }
        
        &.turnover .item-icon {
          background: #f0f9ff;
          color: #67c23a;
        }
        
        .item-content {
          .item-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }
          
          .item-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .inventory-charts {
    margin-bottom: 20px;
    
    .chart-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
  }
  
  .filter-bar,
  .inventory-detail {
    margin-bottom: 20px;
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .zero-stock {
    color: #f56c6c;
    font-weight: 600;
  }
  
  .low-stock {
    color: #e6a23c;
    font-weight: 600;
  }
  
  .excess-stock {
    color: #909399;
    font-weight: 600;
  }
  
  .normal-stock {
    color: #67c23a;
    font-weight: 600;
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .inventory-analysis {
    .overview-grid {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-row {
      grid-template-columns: 1fr;
    }
  }
}
</style>
