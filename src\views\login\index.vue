<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1 class="login-title">BOM协同管理系统</h1>
        <p class="login-subtitle">让小企业也能用得起真正的数字化</p>
      </div>
      
      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>
      
      <!-- 演示账号 -->
      <div class="demo-accounts">
        <h3>演示账号</h3>
        <div class="account-list">
          <div
            v-for="account in demoAccounts"
            :key="account.username"
            class="account-item"
            @click="selectAccount(account)"
          >
            <div class="account-info">
              <span class="account-name">{{ account.name }}</span>
              <span class="account-role">{{ account.role }}</span>
            </div>
            <div class="account-credentials">
              <span>{{ account.username }} / {{ account.password }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

const router = useRouter()
const userStore = useUserStore()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

// 演示账号
const demoAccounts = [
  {
    username: 'admin',
    password: '123456',
    name: '系统管理员',
    role: '拥有所有权限'
  },
  {
    username: 'engineer',
    password: '123456',
    name: '张工程师',
    role: '研发部门，负责设计和工艺BOM'
  },
  {
    username: 'production',
    password: '123456',
    name: '李主管',
    role: '生产部门，负责生产BOM和库存'
  },
  {
    username: 'finance',
    password: '123456',
    name: '王会计',
    role: '财务部门，负责成本分析'
  },
  {
    username: 'purchase',
    password: '123456',
    name: '赵采购',
    role: '采购部门，负责采购计划'
  },
  {
    username: 'sales',
    password: '123456',
    name: '刘销售',
    role: '销售部门，负责客户定制'
  },
  {
    username: 'service',
    password: '123456',
    name: '陈技师',
    role: '售后部门，负责维修服务'
  }
]

const selectAccount = (account) => {
  loginForm.username = account.username
  loginForm.password = account.password
}

const handleLogin = async () => {
  if (!loginFormRef.value) return
  
  try {
    await loginFormRef.value.validate()
    loading.value = true
    
    const result = await userStore.login(loginForm)
    
    if (result.success) {
      ElMessage.success('登录成功')
      router.push('/')
    } else {
      ElMessage.error(result.message || '登录失败')
    }
  } catch (error) {
    console.error('登录失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.login-box {
  background: white;
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  padding: 40px;
  width: 100%;
  max-width: 500px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .login-title {
    font-size: 28px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  .login-subtitle {
    font-size: 14px;
    color: #909399;
    margin: 0;
  }
}

.login-form {
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.demo-accounts {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  
  h3 {
    font-size: 16px;
    color: #303133;
    margin: 0 0 16px 0;
    text-align: center;
  }
  
  .account-list {
    display: grid;
    gap: 8px;
  }
  
  .account-item {
    padding: 12px;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: #409eff;
      background-color: #f0f9ff;
    }
    
    .account-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      
      .account-name {
        font-weight: 500;
        color: #303133;
      }
      
      .account-role {
        font-size: 12px;
        color: #909399;
      }
    }
    
    .account-credentials {
      font-size: 12px;
      color: #606266;
      font-family: 'Courier New', monospace;
    }
  }
}

@media (max-width: 768px) {
  .login-box {
    padding: 30px 20px;
    margin: 20px;
  }
  
  .login-header .login-title {
    font-size: 24px;
  }
}
</style>
