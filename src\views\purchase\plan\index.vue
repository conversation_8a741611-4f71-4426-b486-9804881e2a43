<template>
  <div class="purchase-plan">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">采购计划</h2>
            <p class="page-desc">基于BOM自动生成采购计划，优化库存管理</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="handleGeneratePlan">
              <el-icon><Plus /></el-icon>
              生成采购计划
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 采购需求概览 -->
    <div class="purchase-overview">
      <div class="overview-card content-card">
        <div class="card-header">
          <div class="card-title">采购需求概览</div>
          <div class="card-actions">
            <el-button @click="handleRefresh">刷新</el-button>
          </div>
        </div>
        <div class="card-body">
          <div class="overview-grid">
            <div class="overview-item urgent">
              <div class="item-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.urgentItems }}</div>
                <div class="item-label">紧急采购</div>
              </div>
            </div>
            <div class="overview-item normal">
              <div class="item-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.normalItems }}</div>
                <div class="item-label">常规采购</div>
              </div>
            </div>
            <div class="overview-item total-amount">
              <div class="item-icon">
                <el-icon><Money /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">¥{{ overview.totalAmount.toLocaleString() }}</div>
                <div class="item-label">采购金额</div>
              </div>
            </div>
            <div class="overview-item suppliers">
              <div class="item-icon">
                <el-icon><Shop /></el-icon>
              </div>
              <div class="item-content">
                <div class="item-value">{{ overview.supplierCount }}</div>
                <div class="item-label">涉及供应商</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选条件 -->
    <div class="filter-bar content-card">
      <div class="card-body">
        <el-form :model="filterForm" inline>
          <el-form-item label="优先级">
            <el-select v-model="filterForm.priority" placeholder="请选择优先级" clearable>
              <el-option label="紧急" value="urgent" />
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
          <el-form-item label="供应商">
            <el-select v-model="filterForm.supplier" placeholder="请选择供应商" clearable>
              <el-option
                v-for="supplier in suppliers"
                :key="supplier.id"
                :label="supplier.name"
                :value="supplier.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="物料分类">
            <el-select v-model="filterForm.category" placeholder="请选择分类" clearable>
              <el-option label="主控芯片" value="主控芯片" />
              <el-option label="传感器" value="传感器" />
              <el-option label="显示器件" value="显示器件" />
              <el-option label="被动器件" value="被动器件" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 采购计划列表 -->
    <div class="purchase-list content-card">
      <div class="card-header">
        <div class="card-title">采购计划列表</div>
        <div class="card-actions">
          <el-button type="warning" @click="handleBatchPurchase">
            <el-icon><ShoppingCart /></el-icon>
            批量采购
          </el-button>
          <el-button type="success" @click="handleExport">
            <el-icon><Download /></el-icon>
            导出计划
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="purchaseList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="materialCode" label="物料编码" width="150" />
          <el-table-column prop="materialName" label="物料名称" min-width="200" />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="currentStock" label="当前库存" width="100" />
          <el-table-column prop="safetyStock" label="安全库存" width="100" />
          <el-table-column prop="requiredQty" label="需求数量" width="100" />
          <el-table-column prop="purchaseQty" label="建议采购" width="100">
            <template #default="{ row }">
              <span class="purchase-qty">{{ row.purchaseQty }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unit" label="单位" width="80" />
          <el-table-column prop="unitPrice" label="单价" width="100">
            <template #default="{ row }">
              ¥{{ row.unitPrice.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="采购金额" width="120">
            <template #default="{ row }">
              ¥{{ row.totalAmount.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column prop="supplier" label="供应商" width="120" />
          <el-table-column prop="leadTime" label="交期(天)" width="100" />
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)">
                {{ getPriorityLabel(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="handlePurchase(row)">立即采购</el-button>
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handleRemove(row)" class="danger-text">移除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 采购汇总 -->
    <div class="purchase-summary content-card">
      <div class="card-header">
        <div class="card-title">采购汇总</div>
      </div>
      <div class="card-body">
        <div class="summary-grid">
          <div class="summary-item">
            <label>选中项目:</label>
            <span>{{ selectedItems.length }} 项</span>
          </div>
          <div class="summary-item">
            <label>选中金额:</label>
            <span class="amount-value">¥{{ selectedAmount.toFixed(2) }}</span>
          </div>
          <div class="summary-item">
            <label>总计项目:</label>
            <span>{{ purchaseList.length }} 项</span>
          </div>
          <div class="summary-item">
            <label>总计金额:</label>
            <span class="amount-value">¥{{ totalAmount.toFixed(2) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑采购项对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑采购项"
      width="600px"
    >
      <el-form :model="editForm" label-width="120px">
        <el-form-item label="物料名称">
          <el-input v-model="editForm.materialName" disabled />
        </el-form-item>
        <el-form-item label="建议采购数量">
          <el-input-number
            v-model="editForm.purchaseQty"
            :min="0"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="优先级">
          <el-select v-model="editForm.priority">
            <el-option label="紧急" value="urgent" />
            <el-option label="高" value="high" />
            <el-option label="中" value="medium" />
            <el-option label="低" value="low" />
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="editForm.notes" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const editDialogVisible = ref(false)
const selectedItems = ref([])

const overview = reactive({
  urgentItems: 5,
  normalItems: 23,
  totalAmount: 45680,
  supplierCount: 8
})

const filterForm = reactive({
  priority: '',
  supplier: '',
  category: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const editForm = reactive({
  materialName: '',
  purchaseQty: 0,
  priority: 'medium',
  notes: ''
})

const suppliers = ref([
  { id: '1', name: '意法半导体' },
  { id: '2', name: '乐鑫科技' },
  { id: '3', name: '奥松电子' }
])

const purchaseList = ref([
  {
    id: '1',
    materialCode: 'ESP32-WROOM-32',
    materialName: 'ESP32无线模块',
    category: '无线通信',
    currentStock: 50,
    safetyStock: 20,
    requiredQty: 100,
    purchaseQty: 70,
    unit: '个',
    unitPrice: 18.50,
    totalAmount: 1295.00,
    supplier: '乐鑫科技',
    leadTime: 10,
    priority: 'urgent'
  },
  {
    id: '2',
    materialCode: 'DHT22',
    materialName: 'DHT22温湿度传感器',
    category: '传感器',
    currentStock: 30,
    safetyStock: 30,
    requiredQty: 80,
    purchaseQty: 80,
    unit: '个',
    unitPrice: 12.80,
    totalAmount: 1024.00,
    supplier: '奥松电子',
    leadTime: 8,
    priority: 'high'
  }
])

const selectedAmount = computed(() => {
  return selectedItems.value.reduce((sum, item) => sum + item.totalAmount, 0)
})

const totalAmount = computed(() => {
  return purchaseList.value.reduce((sum, item) => sum + item.totalAmount, 0)
})

const getPriorityType = (priority) => {
  const typeMap = {
    urgent: 'danger',
    high: 'warning',
    medium: 'info',
    low: 'success'
  }
  return typeMap[priority] || 'info'
}

const getPriorityLabel = (priority) => {
  const labelMap = {
    urgent: '紧急',
    high: '高',
    medium: '中',
    low: '低'
  }
  return labelMap[priority] || priority
}

const handleFilter = () => {
  ElMessage.success('筛选条件已应用')
}

const handleReset = () => {
  filterForm.priority = ''
  filterForm.supplier = ''
  filterForm.category = ''
  handleFilter()
}

const handleRefresh = () => {
  ElMessage.success('数据已刷新')
}

const handleGeneratePlan = () => {
  ElMessage.success('采购计划生成成功')
}

const handleSelectionChange = (selection) => {
  selectedItems.value = selection
}

const handleBatchPurchase = () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要采购的物料')
    return
  }
  ElMessage.info(`批量采购功能开发中... (${selectedItems.value.length}项)`)
}

const handlePurchase = (row) => {
  ElMessage.info(`立即采购 ${row.materialName} 功能开发中...`)
}

const handleEdit = (row) => {
  editForm.materialName = row.materialName
  editForm.purchaseQty = row.purchaseQty
  editForm.priority = row.priority
  editForm.notes = row.notes || ''
  editDialogVisible.value = true
}

const handleSaveEdit = () => {
  ElMessage.success('保存成功')
  editDialogVisible.value = false
}

const handleRemove = async (row) => {
  try {
    await ElMessageBox.confirm('确定要移除这个采购项吗？', '确认移除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('移除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('移除失败')
    }
  }
}

const handleExport = () => {
  ElMessage.info('导出采购计划功能开发中...')
}

const handleSizeChange = () => {
  // 处理分页大小变化
}

const handlePageChange = () => {
  // 处理页码变化
}

onMounted(() => {
  pagination.total = purchaseList.value.length
})
</script>

<style lang="scss" scoped>
.purchase-plan {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .purchase-overview {
    margin-bottom: 20px;
    
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      
      .overview-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        background: #f8f9fa;
        
        .item-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 24px;
        }
        
        &.urgent .item-icon {
          background: #fef0f0;
          color: #f56c6c;
        }
        
        &.normal .item-icon {
          background: #f0f9ff;
          color: #409eff;
        }
        
        &.total-amount .item-icon {
          background: #fef0e6;
          color: #e6a23c;
        }
        
        &.suppliers .item-icon {
          background: #f0f9ff;
          color: #67c23a;
        }
        
        .item-content {
          .item-value {
            font-size: 20px;
            font-weight: 600;
            color: #303133;
            line-height: 1;
          }
          
          .item-label {
            font-size: 14px;
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .filter-bar,
  .purchase-list,
  .purchase-summary {
    margin-bottom: 20px;
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .purchase-qty {
    font-weight: 600;
    color: #e6a23c;
  }
  
  .purchase-summary {
    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        label {
          font-weight: 500;
          color: #606266;
        }
        
        span {
          font-weight: 600;
          color: #303133;
          
          &.amount-value {
            color: #e6a23c;
          }
        }
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
