<template>
  <div class="role-management">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">角色管理</h2>
            <p class="page-desc">管理系统角色和权限配置</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建角色
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="role-list content-card">
      <div class="card-header">
        <div class="card-title">角色列表</div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="roleList"
          @row-click="handleRowClick"
        >
          <el-table-column prop="displayName" label="角色名称" width="150" />
          <el-table-column prop="name" label="角色标识" width="150" />
          <el-table-column prop="description" label="角色描述" min-width="200" />
          <el-table-column label="权限数量" width="120">
            <template #default="{ row }">
              {{ row.permissions.length }} 项
            </template>
          </el-table-column>
          <el-table-column label="用户数量" width="120">
            <template #default="{ row }">
              {{ getUserCount(row.name) }} 人
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '启用' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handlePermissions(row)">权限配置</el-button>
              <el-button
                type="text"
                @click="handleDelete(row)"
                class="danger-text"
                v-if="row.name !== 'admin'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 创建/编辑角色对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="roleForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="角色名称" prop="displayName">
          <el-input v-model="roleForm.displayName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="name">
          <el-input
            v-model="roleForm.name"
            placeholder="请输入角色标识"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="roleForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 权限配置对话框 -->
    <el-dialog
      v-model="permissionDialogVisible"
      title="权限配置"
      width="800px"
    >
      <div class="permission-config">
        <div class="role-info">
          <h4>{{ currentRole?.displayName }}</h4>
          <p>{{ currentRole?.description }}</p>
        </div>
        
        <el-divider />
        
        <div class="permission-tree">
          <el-tree
            ref="permissionTreeRef"
            :data="permissionTree"
            :props="treeProps"
            show-checkbox
            node-key="id"
            :default-checked-keys="checkedPermissions"
            @check="handlePermissionCheck"
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePermissions">保存权限</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { mockRoles, mockUsers } from '@/mock/user'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const dialogVisible = ref(false)
const permissionDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const permissionTreeRef = ref()
const currentRole = ref(null)
const checkedPermissions = ref([])

const roleForm = reactive({
  displayName: '',
  name: '',
  description: ''
})

const formRules = {
  displayName: [
    { required: true, message: '请输入角色名称', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入角色标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/, message: '角色标识只能包含字母、数字和下划线，且不能以数字开头', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入角色描述', trigger: 'blur' }
  ]
}

const roleList = ref([])

const treeProps = {
  children: 'children',
  label: 'label'
}

// 权限树结构
const permissionTree = ref([
  {
    id: 'bom',
    label: 'BOM管理',
    children: [
      {
        id: 'bom:design',
        label: '设计BOM',
        children: [
          { id: 'bom:design:view', label: '查看' },
          { id: 'bom:design:create', label: '创建' },
          { id: 'bom:design:edit', label: '编辑' },
          { id: 'bom:design:delete', label: '删除' }
        ]
      },
      {
        id: 'bom:process',
        label: '工艺BOM',
        children: [
          { id: 'bom:process:view', label: '查看' },
          { id: 'bom:process:create', label: '创建' },
          { id: 'bom:process:edit', label: '编辑' },
          { id: 'bom:process:delete', label: '删除' }
        ]
      },
      {
        id: 'bom:production',
        label: '生产BOM',
        children: [
          { id: 'bom:production:view', label: '查看' },
          { id: 'bom:production:create', label: '创建' },
          { id: 'bom:production:edit', label: '编辑' },
          { id: 'bom:production:delete', label: '删除' }
        ]
      },
      {
        id: 'bom:sales',
        label: '销售BOM',
        children: [
          { id: 'bom:sales:view', label: '查看' },
          { id: 'bom:sales:create', label: '创建' },
          { id: 'bom:sales:edit', label: '编辑' },
          { id: 'bom:sales:delete', label: '删除' }
        ]
      },
      {
        id: 'bom:service',
        label: '服务BOM',
        children: [
          { id: 'bom:service:view', label: '查看' },
          { id: 'bom:service:create', label: '创建' },
          { id: 'bom:service:edit', label: '编辑' },
          { id: 'bom:service:delete', label: '删除' }
        ]
      }
    ]
  },
  {
    id: 'cost',
    label: '成本管理',
    children: [
      {
        id: 'cost:analysis',
        label: '成本分析',
        children: [
          { id: 'cost:analysis:view', label: '查看' },
          { id: 'cost:analysis:export', label: '导出' }
        ]
      },
      {
        id: 'cost:report',
        label: '成本报表',
        children: [
          { id: 'cost:report:view', label: '查看' },
          { id: 'cost:report:export', label: '导出' }
        ]
      }
    ]
  },
  {
    id: 'purchase',
    label: '采购管理',
    children: [
      {
        id: 'purchase:plan',
        label: '采购计划',
        children: [
          { id: 'purchase:plan:view', label: '查看' },
          { id: 'purchase:plan:create', label: '创建' },
          { id: 'purchase:plan:edit', label: '编辑' },
          { id: 'purchase:plan:delete', label: '删除' }
        ]
      }
    ]
  },
  {
    id: 'inventory',
    label: '库存管理',
    children: [
      {
        id: 'inventory:analysis',
        label: '库存分析',
        children: [
          { id: 'inventory:analysis:view', label: '查看' },
          { id: 'inventory:analysis:adjust', label: '调整' }
        ]
      }
    ]
  },
  {
    id: 'system',
    label: '系统管理',
    children: [
      {
        id: 'system:user',
        label: '用户管理',
        children: [
          { id: 'system:user:view', label: '查看' },
          { id: 'system:user:create', label: '创建' },
          { id: 'system:user:edit', label: '编辑' },
          { id: 'system:user:delete', label: '删除' }
        ]
      },
      {
        id: 'system:role',
        label: '角色管理',
        children: [
          { id: 'system:role:view', label: '查看' },
          { id: 'system:role:create', label: '创建' },
          { id: 'system:role:edit', label: '编辑' },
          { id: 'system:role:delete', label: '删除' }
        ]
      }
    ]
  }
])

const dialogTitle = computed(() => isEdit.value ? '编辑角色' : '新建角色')

const getUserCount = (roleName) => {
  return mockUsers.filter(user => user.role === roleName).length
}

const loadRoleList = () => {
  loading.value = true
  try {
    // 添加状态字段
    roleList.value = mockRoles.map(role => ({
      ...role,
      status: 'active'
    }))
  } catch (error) {
    ElMessage.error('加载角色列表失败')
  } finally {
    loading.value = false
  }
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleRowClick = (row) => {
  ElMessage.info(`查看角色 ${row.displayName} 详细信息功能开发中...`)
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(roleForm, {
    displayName: row.displayName,
    name: row.name,
    description: row.description
  })
  dialogVisible.value = true
}

const handlePermissions = (row) => {
  currentRole.value = row
  checkedPermissions.value = row.permissions.includes('*') ? 
    getAllPermissionIds() : 
    row.permissions
  permissionDialogVisible.value = true
}

const getAllPermissionIds = () => {
  const ids = []
  const traverse = (nodes) => {
    nodes.forEach(node => {
      if (node.children) {
        traverse(node.children)
      } else {
        ids.push(node.id)
      }
    })
  }
  traverse(permissionTree.value)
  return ids
}

const handlePermissionCheck = (data, checked) => {
  // 权限选择逻辑
}

const handleSavePermissions = () => {
  const checkedKeys = permissionTreeRef.value.getCheckedKeys()
  const halfCheckedKeys = permissionTreeRef.value.getHalfCheckedKeys()
  const allPermissions = [...checkedKeys, ...halfCheckedKeys]
  
  // 更新角色权限
  if (currentRole.value) {
    currentRole.value.permissions = allPermissions
    ElMessage.success('权限配置保存成功')
    permissionDialogVisible.value = false
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除角色 ${row.displayName} 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadRoleList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadRoleList()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(roleForm, {
    displayName: '',
    name: '',
    description: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

onMounted(() => {
  loadRoleList()
})
</script>

<style lang="scss" scoped>
.role-management {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .role-list {
    margin-bottom: 20px;
  }
  
  .permission-config {
    .role-info {
      h4 {
        margin: 0 0 8px 0;
        color: #303133;
      }
      
      p {
        margin: 0;
        color: #606266;
        font-size: 14px;
      }
    }
    
    .permission-tree {
      max-height: 400px;
      overflow-y: auto;
    }
  }
  
  .danger-text {
    color: #f56c6c;
  }
}
</style>
