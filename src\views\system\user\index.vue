<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header content-card">
      <div class="card-body">
        <div class="header-content">
          <div class="header-left">
            <h2 class="page-title">用户管理</h2>
            <p class="page-desc">管理系统用户账号和权限</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="showCreateDialog">
              <el-icon><Plus /></el-icon>
              新建用户
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选 -->
    <div class="search-bar content-card">
      <div class="card-body">
        <el-form :model="searchForm" inline>
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入用户名或姓名"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="searchForm.role" placeholder="请选择角色" clearable>
              <el-option
                v-for="role in roles"
                :key="role.name"
                :label="role.displayName"
                :value="role.name"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="部门">
            <el-select v-model="searchForm.department" placeholder="请选择部门" clearable>
              <el-option label="研发部" value="研发部" />
              <el-option label="生产部" value="生产部" />
              <el-option label="财务部" value="财务部" />
              <el-option label="采购部" value="采购部" />
              <el-option label="销售部" value="销售部" />
              <el-option label="售后服务部" value="售后服务部" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="user-list content-card">
      <div class="card-header">
        <div class="card-title">用户列表</div>
        <div class="card-actions">
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出用户
          </el-button>
        </div>
      </div>
      <div class="card-body">
        <el-table
          v-loading="loading"
          :data="userList"
          @row-click="handleRowClick"
        >
          <el-table-column prop="username" label="用户名" width="120" />
          <el-table-column prop="name" label="姓名" width="100" />
          <el-table-column prop="email" label="邮箱" min-width="180" />
          <el-table-column prop="role" label="角色" width="120">
            <template #default="{ row }">
              <el-tag>{{ getRoleDisplayName(row.role) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="department" label="部门" width="120" />
          <el-table-column label="头像" width="80">
            <template #default="{ row }">
              <el-avatar :src="row.avatar" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                {{ row.status === 'active' ? '正常' : '禁用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="lastLoginTime" label="最后登录" width="160">
            <template #default="{ row }">
              {{ row.lastLoginTime ? formatDate(row.lastLoginTime) : '从未登录' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="text" @click="handleEdit(row)">编辑</el-button>
              <el-button type="text" @click="handleResetPassword(row)">重置密码</el-button>
              <el-button
                type="text"
                @click="handleToggleStatus(row)"
                :class="row.status === 'active' ? 'danger-text' : 'success-text'"
              >
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="text" @click="handleDelete(row)" class="danger-text">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="userForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option
              v-for="role in roles"
              :key="role.name"
              :label="role.displayName"
              :value="role.name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-select v-model="userForm.department" placeholder="请选择部门">
            <el-option label="研发部" value="研发部" />
            <el-option label="生产部" value="生产部" />
            <el-option label="财务部" value="财务部" />
            <el-option label="采购部" value="采购部" />
            <el-option label="销售部" value="销售部" />
            <el-option label="售后服务部" value="售后服务部" />
          </el-select>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input
            v-model="userForm.password"
            type="password"
            placeholder="请输入密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { mockUsers, mockRoles } from '@/mock/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

const searchForm = reactive({
  keyword: '',
  role: '',
  department: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

const userForm = reactive({
  username: '',
  name: '',
  email: '',
  role: '',
  department: '',
  password: ''
})

const formRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  department: [
    { required: true, message: '请选择部门', trigger: 'change' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ]
}

const userList = ref([])
const roles = ref(mockRoles)

const dialogTitle = computed(() => isEdit.value ? '编辑用户' : '新建用户')

const getRoleDisplayName = (roleName) => {
  const role = roles.value.find(r => r.name === roleName)
  return role?.displayName || roleName
}

const formatDate = (dateString) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

const loadUserList = () => {
  loading.value = true
  try {
    // 模拟加载用户数据
    let data = [...mockUsers]
    
    // 添加状态和最后登录时间
    data = data.map(user => ({
      ...user,
      status: 'active',
      lastLoginTime: '2024-03-01T10:00:00Z'
    }))
    
    // 根据搜索条件过滤
    if (searchForm.keyword) {
      data = data.filter(user =>
        user.username.includes(searchForm.keyword) ||
        user.name.includes(searchForm.keyword)
      )
    }
    if (searchForm.role) {
      data = data.filter(user => user.role === searchForm.role)
    }
    if (searchForm.department) {
      data = data.filter(user => user.department === searchForm.department)
    }
    
    userList.value = data
    pagination.total = data.length
  } catch (error) {
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadUserList()
}

const handleReset = () => {
  searchForm.keyword = ''
  searchForm.role = ''
  searchForm.department = ''
  handleSearch()
}

const handleSizeChange = () => {
  loadUserList()
}

const handlePageChange = () => {
  loadUserList()
}

const showCreateDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

const handleRowClick = (row) => {
  ElMessage.info(`查看用户 ${row.name} 详细信息功能开发中...`)
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(userForm, {
    username: row.username,
    name: row.name,
    email: row.email,
    role: row.role,
    department: row.department,
    password: ''
  })
  dialogVisible.value = true
}

const handleResetPassword = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要重置用户 ${row.name} 的密码吗？`, '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('密码重置成功，新密码已发送到用户邮箱')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('密码重置失败')
    }
  }
}

const handleToggleStatus = async (row) => {
  const action = row.status === 'active' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户 ${row.name} 吗？`, `确认${action}`, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    row.status = row.status === 'active' ? 'disabled' : 'active'
    ElMessage.success(`用户${action}成功`)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error(`用户${action}失败`)
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户 ${row.name} 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    ElMessage.success('删除成功')
    loadUserList()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 模拟提交
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
    dialogVisible.value = false
    loadUserList()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  resetForm()
}

const resetForm = () => {
  Object.assign(userForm, {
    username: '',
    name: '',
    email: '',
    role: '',
    department: '',
    password: ''
  })
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleExport = () => {
  ElMessage.info('导出用户功能开发中...')
}

onMounted(() => {
  loadUserList()
})
</script>

<style lang="scss" scoped>
.user-management {
  .page-header {
    margin-bottom: 20px;
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        .page-title {
          font-size: 24px;
          color: #303133;
          margin: 0 0 8px 0;
        }
        
        .page-desc {
          font-size: 14px;
          color: #606266;
          margin: 0;
        }
      }
    }
  }
  
  .search-bar,
  .user-list {
    margin-bottom: 20px;
    
    .card-actions {
      display: flex;
      gap: 8px;
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .danger-text {
    color: #f56c6c;
  }
  
  .success-text {
    color: #67c23a;
  }
}
</style>
