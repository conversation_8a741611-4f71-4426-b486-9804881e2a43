# BOM协同管理系统 - 快速启动指南

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问系统
打开浏览器访问：http://localhost:3000

## 🔑 登录账号

系统提供了7个不同角色的演示账号，所有账号的密码都是：`123456`

### 管理员账号
- **用户名**：`admin`
- **角色**：系统管理员
- **权限**：所有功能权限
- **适用场景**：系统配置、用户管理、全局数据查看

### 研发部门账号
- **用户名**：`engineer`
- **角色**：研发工程师
- **权限**：设计BOM、工艺BOM、成本查看
- **适用场景**：产品设计、工艺开发、技术文档管理

### 生产部门账号
- **用户名**：`production`
- **角色**：生产主管
- **权限**：生产BOM、库存管理、采购查看
- **适用场景**：生产计划、库存控制、生产执行

### 财务部门账号
- **用户名**：`finance`
- **角色**：财务人员
- **权限**：成本分析、成本报表、BOM查看
- **适用场景**：成本核算、财务分析、预算控制

### 采购部门账号
- **用户名**：`purchase`
- **角色**：采购人员
- **权限**：采购计划、库存查看、BOM查看
- **适用场景**：采购管理、供应商协调、库存优化

### 销售部门账号
- **用户名**：`sales`
- **角色**：销售人员
- **权限**：销售BOM、成本查看、设计BOM查看
- **适用场景**：客户定制、产品配置、报价管理

### 售后部门账号
- **用户名**：`service`
- **角色**：售后技师
- **权限**：服务BOM、设计BOM查看、生产BOM查看
- **适用场景**：维修服务、故障诊断、配件管理

## 📋 功能演示流程

### 1. 产品设计流程（engineer账号）
1. 登录系统 → BOM管理 → 设计用料清单
2. 点击"新建BOM"创建产品设计
3. 添加物料，设置数量和位号
4. 查看成本分析

### 2. 工艺开发流程（engineer账号）
1. BOM管理 → 工艺用料清单
2. 创建工艺BOM，添加工艺步骤
3. 为每个步骤配置物料和工时
4. 查看工艺流程图

### 3. 生产管理流程（production账号）
1. BOM管理 → 生产用料清单
2. 创建生产订单BOM
3. 检查库存状态，查看缺料情况
4. 库存管理 → 库存分析，进行库存调整

### 4. 采购管理流程（purchase账号）
1. 采购管理 → 采购计划
2. 查看采购需求概览
3. 生成采购计划，设置优先级
4. 批量采购或单项采购

### 5. 成本分析流程（finance账号）
1. 成本管理 → 成本分析
2. 查看成本概览和分布图
3. 成本管理 → 成本报表
4. 生成各类成本报表

### 6. 客户定制流程（sales账号）
1. BOM管理 → 销售配置清单
2. 创建客户定制配置
3. 设置销售价格
4. 生成报价单

### 7. 售后服务流程（service账号）
1. BOM管理 → 售后用料清单
2. 使用快速查找功能定位产品
3. 创建服务单，选择服务类型
4. 配置维修用料

## 🎯 系统特色功能

### 1. 多类型BOM支持
- **设计BOM**：产品结构定义
- **工艺BOM**：制造工艺路线
- **生产BOM**：生产执行清单
- **销售BOM**：客户定制配置
- **服务BOM**：售后维修清单

### 2. 实时成本计算
- 自动计算物料成本
- 人工成本核算
- 制造费用分摊
- 多维度成本分析

### 3. 智能库存管理
- 实时库存监控
- 低库存预警
- 采购需求计算
- 库存周转分析

### 4. 权限管理体系
- 基于角色的权限控制
- 细粒度功能权限
- 部门数据隔离
- 操作日志记录

## 🔧 技术架构

- **前端**：Vue 3 + Element Plus + Pinia
- **图表**：ECharts
- **构建**：Vite
- **样式**：Sass + BEM规范

## 📞 技术支持

如遇到问题，请检查：
1. Node.js版本是否 >= 16.0.0
2. 网络连接是否正常
3. 端口3000是否被占用

---

**开始您的数字化BOM管理之旅！**
